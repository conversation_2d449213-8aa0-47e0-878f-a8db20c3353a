# 智索应用接口文档

## 目录

- [1. 文档说明](#1-文档说明)
- [2. 接口规范](#2-接口规范)
- [3. 认证模块](#3-认证模块)
- [4. 用户模块](#4-用户模块)
- [5. 文章模块](#5-文章模块)
- [6. 热点话题模块](#6-热点话题模块)
- [7. AI功能模块](#7-ai功能模块)
- [8. 用户交互模块](#8-用户交互模块)
- [9. 搜索模块](#9-搜索模块)
- [10. 上传模块](#10-上传模块)
- [11. 标签模块](#11-标签模块)
- [12. 管理系统接口](#12-管理系统接口)
- [13. 错误码说明](#13-错误码说明)

## 1. 文档说明

本文档描述智索应用生态系统的完整接口设计，包含智索APP和智索管理系统的所有后端接口。接口遵循RESTful设计原则，采用HTTP协议进行通信。

### 1.1 系统架构

- **智索APP后端**: 面向C端用户的移动应用接口，端口8080
- **智索管理系统后端**: 面向B端管理员的Web管理接口，端口8081

### 1.2 接口版本

- **智索APP**: 所有接口以 `/v1` 作为基础路径前缀
- **智索管理系统**: 接口路径直接以功能模块命名

### 1.3 认证说明

- **智索APP**: 部分接口需要JWT认证，AI分析等功能支持匿名访问
- **智索管理系统**: 所有接口均需要管理员JWT认证和权限验证

## 2. 接口规范

### 2.1 请求格式

- **GET请求**: 参数通过URL Query Parameters传递
- **POST/PUT/DELETE请求**: 参数通过JSON格式在Request Body中传递
- **编码格式**: UTF-8
- **Content-Type**: application/json
- **认证头**: Authorization: Bearer {token}

### 2.2 响应格式

统一的JSON响应结构：

```json
{
    "code": 0,            // 状态码，0表示成功，非0表示失败
    "message": "success", // 状态描述
    "data": {             // 业务数据
        // 具体业务数据
    }
}
```

### 2.3 状态码说明

| 状态码 | 说明 |
|--------|------|
| 0 | 请求成功 |
| 400 | 请求参数错误 |
| 401 | 未授权访问 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

### 2.4 认证方式

采用JWT (JSON Web Token)进行身份认证。

- 登录成功后，服务器返回access_token和refresh_token
- 客户端请求需在Header中携带token: `Authorization: Bearer {access_token}`
- token过期时可使用refresh_token刷新，无效时返回401状态码

### 2.5 分页格式

分页接口统一返回格式：

```json
{
    "content": [],        // 数据列表
    "totalElements": 100, // 总记录数
    "totalPages": 5,      // 总页数
    "size": 20,           // 每页大小
    "number": 0           // 当前页码（从0开始）
}
```

### 2.6 服务地址

- **智索APP后端**: http://localhost:8080
- **智索管理系统后端**: http://localhost:8081

## 3. 认证模块 (智索APP)

### 3.1 发送短信验证码

**接口地址**: `/v1/auth/sms/send`

**请求方式**: POST

**是否需要认证**: 否

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| phone | string | 是 | 手机号码 |
| type | string | 是 | 验证码类型(login) |

**请求示例**:

```json
{
    "phone": "13800138000",
    "type": "login"
}
```

**响应示例**:

```json
{
    "code": 0,
    "message": "验证码发送成功",
    "data": {
        "phone": "13800138000",
        "expireTime": 60,
        "message": "验证码已发送，请注意查收（测试环境固定为6666）"
    }
}
```

### 3.2 短信验证码登录

**接口地址**: `/v1/auth/login/sms`

**请求方式**: POST

**是否需要认证**: 否

**说明**: 如果用户不存在将自动注册

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| phone | string | 是 | 手机号码 |
| code | string | 是 | 验证码 |
| deviceId | string | 是 | 设备ID |
| deviceType | string | 是 | 设备类型(android/ios/web) |

**请求示例**:

```json
{
    "phone": "13800138000",
    "code": "6666",
    "deviceId": "device_123456",
    "deviceType": "android"
}
```

**响应示例**:

```json
{
    "code": 0,
    "message": "登录成功",
    "data": {
        "accessToken": "eyJhbGciOiJIUzI1NiJ9...",
        "refreshToken": "eyJhbGciOiJIUzI1NiJ9...",
        "tokenType": "Bearer",
        "expiresIn": 86400,
        "userInfo": {
            "userId": "user123",
            "phone": "13800138000",
            "nickname": "用户昵称",
            "avatar": "头像URL",
            "memberLevel": 0
        }
    }
}
```

### 3.3 密码登录

**接口地址**: `/v1/auth/login/password`

**请求方式**: POST

**是否需要认证**: 否

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| phone | string | 是 | 手机号码 |
| password | string | 是 | 密码(MD5加密) |
| deviceId | string | 是 | 设备ID |
| deviceType | string | 是 | 设备类型 |

**请求示例**:

```json
{
    "phone": "13800138000",
    "password": "e10adc3949ba59abbe56e057f20f883e",
    "deviceId": "device_123456",
    "deviceType": "android"
}
```

**响应示例**: 同短信验证码登录

### 3.4 设置密码

**接口地址**: `/v1/auth/password/set`

**请求方式**: POST

**是否需要认证**: 否

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| phone | string | 是 | 手机号码 |
| code | string | 是 | 短信验证码 |
| password | string | 是 | 新密码(MD5加密) |
| deviceId | string | 是 | 设备ID |
| deviceType | string | 是 | 设备类型 |

**响应示例**: 同登录接口

### 3.5 刷新令牌

**接口地址**: `/v1/auth/token/refresh`

**请求方式**: POST

**是否需要认证**: 否

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| refreshToken | string | 是 | 刷新令牌 |

**响应示例**:

```json
{
    "code": 0,
    "message": "令牌刷新成功",
    "data": {
        "accessToken": "eyJhbGciOiJIUzI1NiJ9...",
        "refreshToken": "eyJhbGciOiJIUzI1NiJ9...",
        "tokenType": "Bearer",
        "expiresIn": 86400
    }
}
```

### 3.6 退出登录

**接口地址**: `/v1/auth/logout`

**请求方式**: POST

**是否需要认证**: 是

**请求参数**: 无

**响应示例**:

```json
{
    "code": 0,
    "message": "退出成功",
    "data": null
}
```

## 4. 用户模块 (智索APP)

### 4.1 获取用户信息

**接口地址**: `/v1/user/info`

**请求方式**: GET

**是否需要认证**: 是

**请求参数**: 无

**响应示例**:

```json
{
    "code": 0,
    "message": "success",
    "data": {
        "userId": "user123",
        "phone": "13800138000",
        "nickname": "用户昵称",
        "avatar": "头像URL",
        "memberLevel": 0,
        "createTime": "2024-01-01T00:00:00",
        "lastLoginTime": "2024-01-01T12:00:00"
    }
}
```

### 4.2 更新用户信息

**接口地址**: `/v1/user/info`

**请求方式**: PUT

**是否需要认证**: 是

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| nickname | string | 否 | 用户昵称 |
| avatar | string | 否 | 头像URL |

**请求示例**:

```json
{
    "nickname": "新昵称",
    "avatar": "新头像URL"
}
```

**响应示例**: 同获取用户信息

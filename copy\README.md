# 智索管理系统

智索应用的现代化管理系统，包含后端API服务和前端管理界面。

## 项目结构

```
zhisuo-app/
├── zhisuo-manager-backend/     # 后端服务 (Spring Boot)
├── zhisuo-manager-frontend/    # 前端界面 (Vue 3)
└── README.md                   # 项目说明
```

## 技术栈

### 后端 (zhisuo-manager-backend)
- **Spring Boot 2.7.6** - Java Web框架
- **Spring Security** - 安全框架
- **MyBatis-Plus 3.5.3** - ORM框架
- **MySQL 8.0** - 数据库
- **Redis** - 缓存和会话存储
- **JWT** - 身份认证
- **Maven** - 依赖管理

### 前端 (zhisuo-manager-frontend)
- **Vue 3** - 前端框架
- **Vite** - 构建工具
- **Element Plus** - UI组件库
- **Pinia** - 状态管理
- **Vue Router** - 路由管理
- **Axios** - HTTP客户端
- **ECharts** - 数据可视化

## 功能特性

### 🔐 认证授权
- JWT Token认证
- 角色权限控制
- 登录状态管理
- 路由守卫

### 👥 用户管理
- 用户列表查询
- 用户状态管理
- 用户详情查看
- 用户统计分析

### 📝 内容管理
- 文章管理
- 热点话题管理
- 标签分类管理
- 内容状态控制

### 📊 数据统计
- 用户增长趋势
- 内容分布统计
- 访问量统计
- 热门内容排行

### ⚙️ 系统管理
- 管理员管理
- 操作日志记录
- 系统配置管理
- 系统信息监控

### 🎨 界面设计
- 现代化UI设计
- 响应式布局
- 深色/浅色主题
- 移动端适配

## 快速开始

### 环境要求

**后端:**
- JDK 8+
- Maven 3.6+
- MySQL 8.0+
- Redis 6.0+

**前端:**
- Node.js 16+
- npm 8+

### 安装步骤

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd zhisuo-app
   ```

2. **启动后端服务**
   ```bash
   cd zhisuo-manager-backend
   
   # 配置数据库连接 (修改 application.yml)
   # 执行数据库初始化脚本 (init.sql)
   
   # 启动服务
   mvn spring-boot:run
   # 或者双击 start.bat (Windows)
   ```

3. **启动前端服务**
   ```bash
   cd zhisuo-manager-frontend
   
   # 安装依赖
   npm install
   
   # 启动开发服务器
   npm run dev
   # 或者双击 start.bat (Windows)
   ```

4. **访问系统**
   - 前端地址: http://localhost:5173
   - 后端地址: http://localhost:8081/manager
   - 默认账号: admin / admin123

### 数据库配置

1. 创建数据库 `zhisuo_manager`
2. 执行初始化脚本 `zhisuo-manager-backend/src/main/resources/init.sql`
3. 修改 `application.yml` 中的数据库连接配置

### Redis配置

确保Redis服务正在运行，默认配置:
- 主机: localhost
- 端口: 6379
- 密码: gcf021206

## 开发指南

### 后端开发

1. **项目结构**
   ```
   src/main/java/com/zhisuo/manager/
   ├── config/         # 配置类
   ├── controller/     # 控制器
   ├── service/        # 服务层
   ├── mapper/         # 数据访问层
   ├── entity/         # 实体类
   ├── dto/           # 数据传输对象
   ├── common/        # 公共类
   └── utils/         # 工具类
   ```

2. **API接口规范**
   - 统一返回格式: `Result<T>`
   - RESTful风格
   - 统一异常处理
   - 接口文档注释

### 前端开发

1. **项目结构**
   ```
   src/
   ├── api/           # API接口
   ├── components/    # 公共组件
   ├── views/         # 页面组件
   ├── router/        # 路由配置
   ├── stores/        # 状态管理
   ├── utils/         # 工具函数
   └── styles/        # 样式文件
   ```

2. **开发规范**
   - Vue 3 Composition API
   - TypeScript支持
   - ESLint代码检查
   - 组件化开发

## 部署说明

### 后端部署

1. **打包应用**
   ```bash
   mvn clean package
   ```

2. **运行JAR包**
   ```bash
   java -jar target/zhisuo-manager-backend-1.0.0.jar
   ```

### 前端部署

1. **构建生产版本**
   ```bash
   npm run build
   ```

2. **部署到Web服务器**
   - 将 `dist` 目录内容部署到Nginx等Web服务器
   - 配置反向代理到后端API

## 许可证

MIT License

## 联系方式

如有问题或建议，请联系开发团队。

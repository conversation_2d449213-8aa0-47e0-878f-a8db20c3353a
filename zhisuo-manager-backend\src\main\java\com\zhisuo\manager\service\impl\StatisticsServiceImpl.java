package com.zhisuo.manager.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zhisuo.manager.entity.User;
import com.zhisuo.manager.mapper.UserMapper;
import com.zhisuo.manager.service.StatisticsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 数据统计服务实现类
 */
@Slf4j
@Service
public class StatisticsServiceImpl implements StatisticsService {
    
    @Autowired
    private UserMapper userMapper;
    
    @Override
    public Map<String, Object> getOverallStatistics() {
        Map<String, Object> statistics = new HashMap<>();
        
        try {
            // 总用户数
            LambdaQueryWrapper<User> userWrapper = new LambdaQueryWrapper<>();
            Long totalUsers = userMapper.selectCount(userWrapper);
            statistics.put("totalUsers", totalUsers);
            
            // 今日新增用户
            String today = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            LambdaQueryWrapper<User> todayUserWrapper = new LambdaQueryWrapper<>();
            todayUserWrapper.ge(User::getCreateTime, today + " 00:00:00")
                    .le(User::getCreateTime, today + " 23:59:59");
            Long todayNewUsers = userMapper.selectCount(todayUserWrapper);
            statistics.put("todayNewUsers", todayNewUsers);
            
            // 模拟其他数据
            statistics.put("totalArticles", 2345L);
            statistics.put("todayArticles", 23L);
            statistics.put("totalTopics", 456L);
            statistics.put("todayTopics", 12L);
            statistics.put("totalViews", 123456L);
            statistics.put("todayViews", 3456L);
            
        } catch (Exception e) {
            log.error("获取总体统计数据失败", e);
            // 返回默认值
            statistics.put("totalUsers", 0L);
            statistics.put("todayNewUsers", 0L);
            statistics.put("totalArticles", 0L);
            statistics.put("todayArticles", 0L);
            statistics.put("totalTopics", 0L);
            statistics.put("todayTopics", 0L);
            statistics.put("totalViews", 0L);
            statistics.put("todayViews", 0L);
        }
        
        return statistics;
    }
    
    @Override
    public Map<String, Object> getUserStatistics() {
        Map<String, Object> statistics = new HashMap<>();
        
        try {
            // 总用户数
            LambdaQueryWrapper<User> totalWrapper = new LambdaQueryWrapper<>();
            Long totalUsers = userMapper.selectCount(totalWrapper);
            statistics.put("totalUsers", totalUsers);
            
            // 活跃用户数
            LambdaQueryWrapper<User> activeWrapper = new LambdaQueryWrapper<>();
            activeWrapper.eq(User::getStatus, 1);
            Long activeUsers = userMapper.selectCount(activeWrapper);
            statistics.put("activeUsers", activeUsers);
            
            // 禁用用户数
            Long disabledUsers = totalUsers - activeUsers;
            statistics.put("disabledUsers", disabledUsers);
            
            // 会员等级分布
            Map<String, Long> memberLevelDistribution = new HashMap<>();
            for (int level = 0; level <= 2; level++) {
                LambdaQueryWrapper<User> levelWrapper = new LambdaQueryWrapper<>();
                levelWrapper.eq(User::getMemberLevel, level);
                Long count = userMapper.selectCount(levelWrapper);
                String levelName = level == 0 ? "普通会员" : (level == 1 ? "黄金会员" : "铂金会员");
                memberLevelDistribution.put(levelName, count);
            }
            statistics.put("memberLevelDistribution", memberLevelDistribution);
            
        } catch (Exception e) {
            log.error("获取用户统计数据失败", e);
            statistics.put("totalUsers", 0L);
            statistics.put("activeUsers", 0L);
            statistics.put("disabledUsers", 0L);
            statistics.put("memberLevelDistribution", new HashMap<>());
        }
        
        return statistics;
    }
    
    @Override
    public Map<String, Object> getContentStatistics() {
        Map<String, Object> statistics = new HashMap<>();
        
        // 模拟内容统计数据
        statistics.put("totalArticles", 2345L);
        statistics.put("publishedArticles", 2100L);
        statistics.put("draftArticles", 245L);
        statistics.put("totalTopics", 456L);
        statistics.put("totalTags", 89L);
        
        // 内容分类分布
        Map<String, Long> categoryDistribution = new HashMap<>();
        categoryDistribution.put("科技资讯", 1048L);
        categoryDistribution.put("财经新闻", 735L);
        categoryDistribution.put("社会热点", 580L);
        categoryDistribution.put("娱乐八卦", 484L);
        categoryDistribution.put("体育新闻", 300L);
        statistics.put("categoryDistribution", categoryDistribution);
        
        return statistics;
    }
    
    @Override
    public Map<String, Object> getVisitStatistics() {
        Map<String, Object> statistics = new HashMap<>();
        
        // 模拟访问统计数据
        statistics.put("totalViews", 123456L);
        statistics.put("todayViews", 3456L);
        statistics.put("uniqueVisitors", 45678L);
        statistics.put("todayUniqueVisitors", 1234L);
        statistics.put("avgViewTime", "2分35秒");
        statistics.put("bounceRate", "35.6%");
        
        return statistics;
    }
    
    @Override
    public Map<String, Object> getUserGrowthTrend(Integer days) {
        Map<String, Object> trendData = new HashMap<>();
        
        if (days == null || days <= 0) {
            days = 7; // 默认7天
        }
        
        List<String> dates = new ArrayList<>();
        List<Long> userCounts = new ArrayList<>();
        
        // 生成最近N天的日期和模拟数据
        LocalDateTime now = LocalDateTime.now();
        for (int i = days - 1; i >= 0; i--) {
            LocalDateTime date = now.minusDays(i);
            dates.add(date.format(DateTimeFormatter.ofPattern("MM-dd")));
            
            // 模拟用户增长数据
            userCounts.add((long) (Math.random() * 100 + 50));
        }
        
        trendData.put("dates", dates);
        trendData.put("userCounts", userCounts);
        
        return trendData;
    }
    
    @Override
    public Map<String, Object> getContentDistribution() {
        Map<String, Object> distribution = new HashMap<>();
        
        // 模拟内容分布数据
        List<Map<String, Object>> data = new ArrayList<>();
        
        Map<String, Object> tech = new HashMap<>();
        tech.put("name", "科技资讯");
        tech.put("value", 1048);
        data.add(tech);
        
        Map<String, Object> finance = new HashMap<>();
        finance.put("name", "财经新闻");
        finance.put("value", 735);
        data.add(finance);
        
        Map<String, Object> social = new HashMap<>();
        social.put("name", "社会热点");
        social.put("value", 580);
        data.add(social);
        
        Map<String, Object> entertainment = new HashMap<>();
        entertainment.put("name", "娱乐八卦");
        entertainment.put("value", 484);
        data.add(entertainment);
        
        Map<String, Object> sports = new HashMap<>();
        sports.put("name", "体育新闻");
        sports.put("value", 300);
        data.add(sports);
        
        distribution.put("data", data);
        
        return distribution;
    }
    
    @Override
    public Map<String, Object> getPopularContent(Integer limit) {
        Map<String, Object> popularContent = new HashMap<>();
        
        if (limit == null || limit <= 0) {
            limit = 10; // 默认10条
        }
        
        // 模拟热门文章数据
        List<Map<String, Object>> articles = new ArrayList<>();
        for (int i = 1; i <= Math.min(limit, 10); i++) {
            Map<String, Object> article = new HashMap<>();
            article.put("title", "热门文章标题 " + i);
            article.put("viewCount", (long) (Math.random() * 10000 + 1000));
            article.put("likeCount", (long) (Math.random() * 1000 + 100));
            article.put("commentCount", (long) (Math.random() * 500 + 50));
            articles.add(article);
        }
        
        // 模拟热门话题数据
        List<Map<String, Object>> topics = new ArrayList<>();
        for (int i = 1; i <= Math.min(limit, 10); i++) {
            Map<String, Object> topic = new HashMap<>();
            topic.put("title", "热门话题 " + i);
            topic.put("hotValue", String.valueOf((long) (Math.random() * 100000 + 10000)));
            topic.put("searchCount", (long) (Math.random() * 5000 + 500));
            topics.add(topic);
        }
        
        popularContent.put("articles", articles);
        popularContent.put("topics", topics);
        
        return popularContent;
    }
}

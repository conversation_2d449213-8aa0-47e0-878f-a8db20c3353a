-- 智索APP数据库创建脚本
-- 适用于MySQL 5.7及以上版本
-- 创建日期: 2024-07-01

-- 创建数据库
DROP DATABASE IF EXISTS zhisuo_app;
CREATE DATABASE zhisuo_app DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE zhisuo_app;

-- 用户表
CREATE TABLE `users` (
    `user_id` VARCHAR(32) PRIMARY KEY COMMENT '用户唯一标识',
    `phone` VARCHAR(15) NOT NULL UNIQUE COMMENT '手机号(登录账号)',
    `password_hash` VARCHAR(128) DEFAULT NULL COMMENT '密码哈希值',
    `password_salt` VARCHAR(32) DEFAULT NULL COMMENT '密码盐值',
    `nickname` VARCHAR(50) DEFAULT NULL COMMENT '用户昵称',
    `avatar` VARCHAR(255) DEFAULT NULL COMMENT '头像URL',
    `member_level` TINYINT(4) DEFAULT 0 COMMENT '会员等级(0:普通,1:黄金,2:铂金等)',
    `create_time` DATETIME NOT NULL COMMENT '注册时间',
    `update_time` DATETIME NOT NULL COMMENT '更新时间',
    `last_login_time` DATETIME DEFAULT NULL COMMENT '最后登录时间',
    `status` TINYINT(4) DEFAULT 1 COMMENT '状态(1:正常,0:禁用)'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 用户令牌表
CREATE TABLE `user_tokens` (
    `token_id` VARCHAR(32) PRIMARY KEY COMMENT '令牌ID',
    `user_id` VARCHAR(32) NOT NULL COMMENT '关联用户ID',
    `token` VARCHAR(255) NOT NULL COMMENT '令牌值',
    `device_type` VARCHAR(20) NOT NULL COMMENT '设备类型',
    `expire_time` DATETIME NOT NULL COMMENT '过期时间',
    `create_time` DATETIME NOT NULL COMMENT '创建时间',
    `last_active_time` DATETIME NOT NULL COMMENT '最后活跃时间',
    CONSTRAINT `fk_token_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户令牌表';

-- 用户搜索历史表
CREATE TABLE `user_search_history` (
    `history_id` VARCHAR(32) PRIMARY KEY COMMENT '历史记录ID',
    `user_id` VARCHAR(32) NOT NULL COMMENT '关联用户ID',
    `keyword` VARCHAR(100) NOT NULL COMMENT '搜索关键词',
    `search_time` DATETIME NOT NULL COMMENT '搜索时间',
    CONSTRAINT `fk_history_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户搜索历史表';

-- 文章内容表
CREATE TABLE `articles` (
    `article_id` VARCHAR(32) PRIMARY KEY COMMENT '文章ID',
    `title` VARCHAR(500) NOT NULL COMMENT '文章标题',
    `description` VARCHAR(2000) DEFAULT NULL COMMENT '文章描述',
    `content` TEXT NOT NULL COMMENT '文章内容',
    `cover_image` VARCHAR(255) DEFAULT NULL COMMENT '封面图URL',
    `source` VARCHAR(50) DEFAULT NULL COMMENT '内容来源',
    `source_url` VARCHAR(1000) DEFAULT NULL COMMENT '来源URL',
    `icon_url` VARCHAR(1000) DEFAULT NULL COMMENT '图标URL',
    `view_count` INT(11) DEFAULT 0 COMMENT '阅读量',
    `comment_count` INT(11) DEFAULT 0 COMMENT '评论量',
    `like_count` INT(11) DEFAULT 0 COMMENT '点赞量',
    `is_read` TINYINT(1) DEFAULT 0 COMMENT '是否已读(1:是,0:否)',
    `is_favorite` TINYINT(1) DEFAULT 0 COMMENT '是否已收藏(1:是,0:否)',
    `collect_time` DATETIME DEFAULT NULL COMMENT '收集时间(热点内容)',
    `publish_time` DATETIME NOT NULL COMMENT '发布时间',
    `create_time` DATETIME NOT NULL COMMENT '创建时间',
    `update_time` DATETIME NOT NULL COMMENT '更新时间',
    `status` TINYINT(4) DEFAULT 1 COMMENT '状态(1:已发布,0:草稿,-1:已删除)'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文章内容表';

-- 热点话题表
CREATE TABLE `hot_topics` (
    `topic_id` VARCHAR(32) PRIMARY KEY COMMENT '话题ID',
    `title` VARCHAR(500) NOT NULL COMMENT '话题标题',
    `description` VARCHAR(2000) DEFAULT NULL COMMENT '话题描述',
    `source` VARCHAR(50) DEFAULT NULL COMMENT '来源平台(baidu/tieba/toutiao/ithome/zhihu等)',
    `source_url` VARCHAR(1000) DEFAULT NULL COMMENT '来源URL',
    `hot_value` VARCHAR(50) DEFAULT NULL COMMENT '热度值',
    `view_count` INT(11) DEFAULT 0 COMMENT '阅读量',
    `search_count` INT(11) DEFAULT 0 COMMENT '搜索量',
    `trend` TINYINT(4) DEFAULT 0 COMMENT '趋势(1:上升,0:持平,-1:下降)',
    `rank` INT(11) DEFAULT NULL COMMENT '热榜排名',
    `is_read` TINYINT(1) DEFAULT 0 COMMENT '是否已读(1:是,0:否)',
    `is_favorite` TINYINT(1) DEFAULT 0 COMMENT '是否已收藏(1:是,0:否)',
    `collect_time` DATETIME DEFAULT NULL COMMENT '收集时间',
    `update_time` DATETIME NOT NULL COMMENT '更新时间',
    `status` TINYINT(4) DEFAULT 1 COMMENT '状态(1:显示,0:不显示)'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='热点话题表';

-- 标签分类表
CREATE TABLE `tags` (
    `tag_id` VARCHAR(32) PRIMARY KEY COMMENT '标签ID',
    `tag_name` VARCHAR(50) NOT NULL COMMENT '标签名称',
    `category` VARCHAR(30) DEFAULT NULL COMMENT '标签分类',
    `icon` VARCHAR(255) DEFAULT NULL COMMENT '标签图标',
    `create_time` DATETIME NOT NULL COMMENT '创建时间',
    `status` TINYINT(4) DEFAULT 1 COMMENT '状态(1:启用,0:禁用)'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='标签分类表';

-- 内容标签关联表
CREATE TABLE `content_tags` (
    `id` VARCHAR(32) PRIMARY KEY COMMENT '主键',
    `content_id` VARCHAR(32) NOT NULL COMMENT '内容ID(文章或热点)',
    `content_type` VARCHAR(20) NOT NULL COMMENT '内容类型(article/topic)',
    `tag_id` VARCHAR(32) NOT NULL COMMENT '标签ID',
    `create_time` DATETIME NOT NULL COMMENT '创建时间',
    CONSTRAINT `fk_content_tag_tag` FOREIGN KEY (`tag_id`) REFERENCES `tags` (`tag_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='内容标签关联表';

-- 用户兴趣标签表
CREATE TABLE `user_interests` (
    `interest_id` VARCHAR(32) PRIMARY KEY COMMENT '兴趣标签ID',
    `user_id` VARCHAR(32) NOT NULL COMMENT '用户ID',
    `tag_id` VARCHAR(32) NOT NULL COMMENT '标签ID',
    `weight` FLOAT DEFAULT 0 COMMENT '权重值',
    `create_time` DATETIME NOT NULL COMMENT '创建时间',
    `update_time` DATETIME NOT NULL COMMENT '更新时间',
    CONSTRAINT `fk_interest_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE,
    CONSTRAINT `fk_interest_tag` FOREIGN KEY (`tag_id`) REFERENCES `tags` (`tag_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户兴趣标签表';

-- 用户收藏表
CREATE TABLE `user_favorites` (
    `favorite_id` VARCHAR(32) PRIMARY KEY COMMENT '收藏ID',
    `user_id` VARCHAR(32) NOT NULL COMMENT '用户ID',
    `content_id` VARCHAR(32) NOT NULL COMMENT '内容ID',
    `content_type` VARCHAR(20) NOT NULL COMMENT '内容类型(article/topic)',
    `create_time` DATETIME NOT NULL COMMENT '收藏时间',
    CONSTRAINT `fk_favorite_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户收藏表';

-- 用户点赞表
CREATE TABLE `user_likes` (
    `like_id` VARCHAR(32) PRIMARY KEY COMMENT '点赞ID',
    `user_id` VARCHAR(32) NOT NULL COMMENT '用户ID',
    `content_id` VARCHAR(32) NOT NULL COMMENT '内容ID',
    `content_type` VARCHAR(20) NOT NULL COMMENT '内容类型(article/topic/comment)',
    `create_time` DATETIME NOT NULL COMMENT '点赞时间',
    CONSTRAINT `fk_like_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户点赞表';

-- 评论表
CREATE TABLE `comments` (
    `comment_id` VARCHAR(32) PRIMARY KEY COMMENT '评论ID',
    `content_id` VARCHAR(32) NOT NULL COMMENT '内容ID',
    `content_type` VARCHAR(20) NOT NULL COMMENT '内容类型(article/topic)',
    `user_id` VARCHAR(32) NOT NULL COMMENT '用户ID',
    `content` VARCHAR(1000) NOT NULL COMMENT '评论内容',
    `parent_id` VARCHAR(32) DEFAULT NULL COMMENT '父评论ID(回复)',
    `like_count` INT(11) DEFAULT 0 COMMENT '点赞数',
    `create_time` DATETIME NOT NULL COMMENT '评论时间',
    `status` TINYINT(4) DEFAULT 1 COMMENT '状态(1:显示,0:隐藏)',
    CONSTRAINT `fk_comment_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='评论表';

-- 系统通知表
CREATE TABLE `system_notices` (
    `notice_id` VARCHAR(32) PRIMARY KEY COMMENT '通知ID',
    `title` VARCHAR(100) NOT NULL COMMENT '通知标题',
    `content` TEXT NOT NULL COMMENT '通知内容',
    `notice_type` TINYINT(4) DEFAULT 0 COMMENT '通知类型(0:系统通知,1:版本更新,2:活动通知)',
    `image_url` VARCHAR(255) DEFAULT NULL COMMENT '图片URL',
    `publish_time` DATETIME NOT NULL COMMENT '发布时间',
    `expire_time` DATETIME DEFAULT NULL COMMENT '过期时间',
    `status` TINYINT(4) DEFAULT 1 COMMENT '状态(1:有效,0:无效)'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统通知表';

-- 用户通知表
CREATE TABLE `user_notices` (
    `id` VARCHAR(32) PRIMARY KEY COMMENT '主键',
    `user_id` VARCHAR(32) NOT NULL COMMENT '用户ID',
    `notice_id` VARCHAR(32) NOT NULL COMMENT '通知ID',
    `is_read` TINYINT(4) DEFAULT 0 COMMENT '是否已读(1:已读,0:未读)',
    `read_time` DATETIME DEFAULT NULL COMMENT '阅读时间',
    CONSTRAINT `fk_user_notice_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE,
    CONSTRAINT `fk_user_notice_notice` FOREIGN KEY (`notice_id`) REFERENCES `system_notices` (`notice_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户通知表';

-- 用户分析报告表
CREATE TABLE `user_analysis` (
    `analysis_id` VARCHAR(32) PRIMARY KEY COMMENT '分析ID',
    `user_id` VARCHAR(32) NOT NULL COMMENT '用户ID',
    `title` VARCHAR(100) NOT NULL COMMENT '分析标题',
    `description` VARCHAR(500) DEFAULT NULL COMMENT '分析描述',
    `content` TEXT NOT NULL COMMENT '分析内容',
    `hot_value` INT(11) DEFAULT 0 COMMENT '热度值',
    `comment_count` INT(11) DEFAULT 0 COMMENT '评论数',
    `create_time` DATETIME NOT NULL COMMENT '创建时间',
    `update_time` DATETIME NOT NULL COMMENT '更新时间',
    `status` TINYINT(4) DEFAULT 1 COMMENT '状态(1:显示,0:隐藏)',
    CONSTRAINT `fk_analysis_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户分析报告表';

-- 协议表
CREATE TABLE `agreements` (
    `agreement_id` VARCHAR(32) PRIMARY KEY COMMENT '协议ID',
    `title` VARCHAR(100) NOT NULL COMMENT '协议标题',
    `content` TEXT NOT NULL COMMENT '协议内容',
    `type` VARCHAR(20) NOT NULL COMMENT '协议类型(user/privacy)',
    `version` VARCHAR(20) NOT NULL COMMENT '版本号',
    `publish_time` DATETIME NOT NULL COMMENT '发布时间',
    `status` TINYINT(4) DEFAULT 1 COMMENT '状态(1:生效,0:失效)'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='协议表';

-- 创建索引
-- 用户表索引
CREATE INDEX `idx_user_member_level` ON `users` (`member_level`);

-- 用户搜索历史表索引
CREATE INDEX `idx_search_history_user_keyword` ON `user_search_history` (`user_id`, `keyword`);
CREATE INDEX `idx_search_history_time` ON `user_search_history` (`search_time`);

-- 文章表索引
CREATE INDEX `idx_article_title` ON `articles` (`title`);
CREATE INDEX `idx_article_source` ON `articles` (`source`);
CREATE INDEX `idx_article_collect_time` ON `articles` (`collect_time`);
CREATE INDEX `idx_article_publish_time` ON `articles` (`publish_time`);
CREATE INDEX `idx_article_status_publish` ON `articles` (`status`, `publish_time`);

-- 热点话题表索引
CREATE INDEX `idx_topic_title` ON `hot_topics` (`title`);
CREATE INDEX `idx_topic_source` ON `hot_topics` (`source`);
CREATE INDEX `idx_topic_hot_value` ON `hot_topics` (`hot_value`);
CREATE INDEX `idx_topic_collect_time` ON `hot_topics` (`collect_time`);
CREATE UNIQUE INDEX `idx_topic_rank` ON `hot_topics` (`rank`);
CREATE INDEX `idx_topic_status_hot` ON `hot_topics` (`status`, `hot_value`);

-- 用户收藏表索引
CREATE INDEX `idx_favorite_user_type` ON `user_favorites` (`user_id`, `content_type`);
CREATE INDEX `idx_favorite_create_time` ON `user_favorites` (`create_time`);

-- 用户点赞表索引
CREATE INDEX `idx_like_user_type` ON `user_likes` (`user_id`, `content_type`);
CREATE INDEX `idx_like_create_time` ON `user_likes` (`create_time`);

-- 添加初始化数据
-- 初始化协议数据
INSERT INTO `agreements` (`agreement_id`, `title`, `content`, `type`, `version`, `publish_time`, `status`) VALUES
('AGR00000000000000000000000000001', '用户协议', '欢迎使用智索平台！\n\n本用户协议（以下简称"本协议"）是您与智索平台之间关于使用智索平台服务所订立的协议。请您仔细阅读本协议的全部内容（特别是以粗体标注的内容）。\n\n一、服务条款\n\n1.1 本协议是您与智索平台之间关于您使用智索平台服务所订立的协议。\n\n1.2 您在使用智索平台服务前，应当认真阅读本协议的全部内容。', 'user', 'v1.0', '2023-01-01 00:00:00', 1),
('AGR00000000000000000000000000002', '隐私协议', '智索平台隐私政策\n\n我们非常重视您的隐私保护，本隐私政策说明了我们如何收集、使用、存储和分享您的个人信息，以及您可以如何访问、更新、控制和保护您的个人信息。\n\n一、我们收集的信息\n\n1.1 您提供的信息：当您注册账号、使用我们的服务或与我们联系时提供的信息。', 'privacy', 'v1.0', '2023-01-01 00:00:00', 1);

-- 初始化标签数据
INSERT INTO `tags` (`tag_id`, `tag_name`, `category`, `icon`, `create_time`, `status`) VALUES
('TAG00000000000000000000000000001', '科技', '热点', 'icon-tech.png', '2023-01-01 00:00:00', 1),
('TAG00000000000000000000000000002', '财经', '热点', 'icon-finance.png', '2023-01-01 00:00:00', 1),
('TAG00000000000000000000000000003', '体育', '热点', 'icon-sports.png', '2023-01-01 00:00:00', 1),
('TAG00000000000000000000000000004', '娱乐', '热点', 'icon-entertainment.png', '2023-01-01 00:00:00', 1),
('TAG00000000000000000000000000005', '教育', '热点', 'icon-education.png', '2023-01-01 00:00:00', 1); 
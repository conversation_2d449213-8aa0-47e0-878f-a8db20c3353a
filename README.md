# 智索应用生态系统

智索应用的完整生态系统，包含移动端APP应用和Web端管理系统，为用户提供内容发现、热点分析、AI智能分析和个性化推荐服务。

## 项目概括

智索是一个集成了内容聚合、热点分析、AI智能分析和用户管理的综合性应用平台。项目采用前后端分离架构，包含以下四个核心子系统：

1. **智索APP** - 面向C端用户的移动应用，基于uni-app开发，支持多端发布
   - 内容浏览：央视新闻等权威媒体内容聚合
   - 热点发现：实时热点话题追踪和分析
   - AI分析：基于硅基流动AI的智能内容分析
   - 个人中心：用户资料、收藏、分析记录管理

2. **智索管理系统** - 面向B端管理员的Web管理平台，基于Vue 3 + Element Plus开发
   - 用户管理：用户信息查看、状态管理、统计分析
   - 内容管理：文章和热点话题的审核、编辑、发布
   - 数据统计：用户增长、内容分布、访问量等数据可视化
   - 系统管理：管理员权限、操作日志、系统配置

## 项目结构

```
zhisuo-app/
├── zhisuo-app-backend/         # APP后端服务 (Spring Boot 2.7.6)
├── zhisuo-app-frontend/        # APP前端应用 (uni-app)
├── zhisuo-manager-backend/     # 管理系统后端服务 (Spring Boot 2.7.6)
├── zhisuo-manager-frontend/    # 管理系统前端界面 (Vue 3 + Vite)
├── copy/                       # 备份文件夹
├── README.md                   # 项目总体说明
├── 智索接口文档.md              # API接口文档
├── 智索数据库设计文档.md        # 数据库设计文档
└── 智索数据库设计.sql          # 数据库建表脚本
```

## 技术选型

### 智索APP后端 (zhisuo-app-backend)
- **主要编程语言**: Java 8
- **核心框架**: Spring Boot 2.7.6
- **ORM框架**: MyBatis-Plus 3.5.3
- **数据库**: MySQL 8.0.31
- **缓存**: Redis
- **身份认证**: JWT (jjwt 0.11.5)
- **JSON处理**: FastJSON 2.0.32
- **工具库**: Hutool 5.8.15
- **文件存储**: 阿里云OSS 3.16.1
- **AI服务**: 硅基流动AI (Qwen/Qwen3-8B)
- **构建工具**: Maven
- **API风格**: RESTful

### 智索APP前端 (zhisuo-app-frontend)
- **开发框架**: uni-app (基于Vue.js)
- **UI组件**: uni-ui 1.5.8
- **跨平台支持**: 微信小程序、H5、APP
- **开发工具**: HBuilderX
- **页面路由**: pages.json配置
- **网络请求**: uni.request

### 智索管理系统后端 (zhisuo-manager-backend)
- **主要编程语言**: Java 8
- **核心框架**: Spring Boot 2.7.6
- **安全框架**: Spring Security
- **ORM框架**: MyBatis-Plus 3.5.3
- **数据库**: MySQL 8.0.31
- **缓存**: Redis
- **身份认证**: JWT (jjwt 0.11.5)
- **JSON处理**: FastJSON 2.0.32
- **工具库**: Hutool 5.8.15
- **文件存储**: 阿里云OSS 3.16.1
- **构建工具**: Maven

### 智索管理系统前端 (zhisuo-manager-frontend)
- **前端框架**: Vue 3.4.0
- **构建工具**: Vite 5.0.0
- **UI组件库**: Element Plus 2.4.0
- **状态管理**: Pinia 2.1.0
- **路由管理**: Vue Router 4.2.0
- **HTTP客户端**: Axios 1.6.0
- **数据可视化**: ECharts 5.4.0
- **图标库**: @element-plus/icons-vue 2.3.0
- **进度条**: NProgress 0.2.0
- **Cookie管理**: js-cookie 3.0.5
- **代码检查**: ESLint 8.55.0 + Prettier 3.1.0
- **自动导入**: unplugin-auto-import + unplugin-vue-components

## 项目结构 / 模块划分

### 智索APP (zhisuo-app)
```
zhisuo-app-backend/
├── src/main/java/com/zhisuo/app/
│   ├── ZhisuoAppApplication.java       # 应用启动类
│   ├── common/                         # 通用模块
│   │   ├── ErrorCode.java              # 错误码枚举
│   │   ├── Result.java                 # 通用响应结果类
│   │   ├── context/                    # 上下文管理
│   │   └── exception/                  # 异常处理
│   ├── config/                         # 配置类
│   ├── controller/                     # 控制器层
│   ├── dto/                           # 数据传输对象
│   ├── entity/                        # 实体类
│   ├── interceptor/                   # 拦截器
│   ├── mapper/                        # 数据访问层
│   ├── service/                       # 业务逻辑层
│   └── util/                          # 工具类
├── src/main/resources/
│   ├── application.yml                # 应用配置
│   └── mapper/                        # MyBatis映射文件
├── 智索APP接口文档.md                  # API接口文档
├── 智索APP数据库设计.sql              # 数据库脚本
└── 智索APP数据库设计文档.md           # 数据库设计文档

zhisuo-app-frontend/
├── pages/                             # 页面组件
│   ├── index/                         # 首页
│   ├── discover/                      # 发现页
│   ├── mine/                          # 个人中心
│   ├── login/                         # 登录相关
│   ├── article/                       # 文章相关
│   ├── analysis/                      # 分析相关
│   ├── favority/                      # 收藏相关
│   └── common/                        # 通用页面
├── components/                        # 公共组件
├── static/                           # 静态资源
├── common/                           # 公共工具
├── pages.json                        # 页面配置
├── manifest.json                     # 应用配置
└── uni.scss                          # 全局样式
```

### 智索管理系统 (zhisuo-manager)
```
zhisuo-manager-backend/
├── src/main/java/com/zhisuo/manager/
│   ├── config/                        # 配置类
│   ├── controller/                    # 控制器层
│   ├── service/                       # 业务逻辑层
│   ├── mapper/                        # 数据访问层
│   ├── entity/                        # 实体类
│   ├── dto/                          # 数据传输对象
│   ├── common/                       # 公共类
│   └── utils/                        # 工具类
├── src/main/resources/
│   ├── application.yml               # 应用配置
│   ├── init.sql                      # 初始化脚本
│   └── mapper/                       # MyBatis映射文件
└── start.bat                         # 启动脚本

zhisuo-manager-frontend/
├── src/
│   ├── api/                          # API接口
│   ├── components/                   # 公共组件
│   ├── views/                        # 页面组件
│   ├── router/                       # 路由配置
│   ├── stores/                       # 状态管理
│   ├── utils/                        # 工具函数
│   └── styles/                       # 样式文件
├── public/                           # 静态资源
├── vite.config.js                    # Vite配置
└── start.bat                         # 启动脚本
```

## 核心功能 / 模块详解

### 智索APP核心功能

#### 1. 用户认证模块 (`AuthController`)
- **短信验证码登录**: `/v1/auth/login/sms` - 支持手机号验证码登录，新用户自动注册
- **密码登录**: `/v1/auth/login/password` - 支持手机号+密码登录
- **验证码发送**: `/v1/auth/sms/send` - 发送短信验证码（测试环境固定6666）
- **密码设置**: `/v1/auth/password/set` - 首次设置登录密码
- **令牌刷新**: `/v1/auth/token/refresh` - 刷新访问令牌
- **退出登录**: `/v1/auth/logout` - 清除用户会话

#### 2. 内容浏览模块 (`ArticleController`)
- **文章列表**: `/v1/articles` - 分页获取文章列表，支持来源筛选和排序
- **文章详情**: `/v1/articles/{articleId}` - 获取文章详细内容和标签信息
- **浏览量更新**: `/v1/articles/{articleId}/view` - 更新文章浏览量
- **文章搜索**: `/v1/articles/search` - 基于关键词搜索文章内容
- **央视新闻更新**: `/v1/articles/refresh-cctv` - 手动触发央视新闻内容更新

#### 3. 热点发现模块 (`HotTopicController`)
- **热点列表**: `/v1/hot-topics` - 获取热点话题列表，支持来源和排序
- **热点详情**: `/v1/hot-topics/{topicId}` - 获取热点话题详细信息
- **热点搜索**: `/v1/hot-topics/search` - 基于关键词搜索热点话题

#### 4. AI智能分析模块 (`AIController`)
- **内容分析**: `/v1/ai/analyze` - 基于硅基流动AI的智能内容分析
- **分析记录**: `/v1/ai/analysis/{analysisId}` - 获取历史分析记录详情
- **删除分析**: `/v1/ai/analysis/{analysisId}` - 删除分析记录

#### 5. 用户交互模块 (`CommentController`, `LikeController`, `FavoriteController`)
- **评论管理**: 发表评论、获取评论列表、删除评论
- **点赞功能**: 文章/话题/评论点赞和取消点赞
- **收藏功能**: 内容收藏、取消收藏、收藏列表管理

#### 6. 个人中心模块 (`UserController`)
- **用户信息**: `/v1/user/info` - 获取和更新用户基本信息
- **密码修改**: 修改登录密码
- **头像上传**: 用户头像上传和更新

#### 7. 搜索模块 (`SearchController`)
- **综合搜索**: 支持文章和热点话题的统一搜索
- **搜索历史**: 用户搜索记录管理
- **热门搜索**: 热门搜索关键词统计

### 智索管理系统核心功能

#### 1. 认证授权模块 (`AuthController`)
- **管理员登录**: `/auth/login` - 管理员账号密码登录
- **权限控制**: 基于Spring Security的角色权限管理
- **会话管理**: JWT令牌管理和自动续期

#### 2. 用户管理模块 (`UserController`)
- **用户列表**: `/users` - 分页查询用户列表，支持多条件筛选
- **用户详情**: `/users/{userId}` - 查看用户详细信息和统计数据
- **用户状态**: 用户启用/禁用状态管理
- **用户统计**: 用户增长趋势、活跃度分析

#### 3. 内容管理模块 (`ArticleController`, `HotTopicController`)
- **文章管理**: 文章列表、详情查看、状态管理、批量操作
- **热点管理**: 热点话题审核、编辑、发布、下架
- **标签管理**: 内容标签分类和关联管理
- **内容统计**: 阅读量、评论量、点赞量等数据统计

#### 4. 数据统计模块 (`StatisticsController`)
- **用户统计**: 用户注册趋势、活跃用户分析、用户画像
- **内容统计**: 文章发布量、热点话题趋势、内容分布
- **访问统计**: 页面访问量、用户行为分析、热门内容排行
- **数据可视化**: 基于ECharts的图表展示

#### 5. 系统管理模块 (`AdminController`, `SystemController`)
- **管理员管理**: 管理员账号创建、权限分配、状态管理
- **操作日志**: 管理员操作记录和审计日志
- **系统配置**: 系统参数配置和管理
- **健康检查**: `/health` - 系统运行状态监控

## 数据模型

### 智索APP数据模型
- **User**: 用户基础信息表 (用户ID、手机号、昵称、头像、注册时间等)
- **UserToken**: 用户令牌表 (访问令牌、刷新令牌、设备信息、过期时间等)
- **Article**: 文章信息表 (文章ID、标题、内容、作者、发布时间、状态等)
- **Comment**: 评论信息表 (评论ID、文章ID、用户ID、评论内容、评论时间等)
- **HotTopic**: 热点话题表 (话题ID、话题标题、热度值、创建时间、状态等)
- **UserFavorite**: 用户收藏表 (收藏ID、用户ID、文章ID、收藏时间等)
- **SystemNotice**: 系统通知表 (通知ID、标题、内容、发布时间、状态等)

### 智索管理系统数据模型
- **Admin**: 管理员信息表 (管理员ID、用户名、密码、角色、权限等)
- **OperationLog**: 操作日志表 (日志ID、操作人、操作类型、操作时间、详情等)
- **SystemConfig**: 系统配置表 (配置项、配置值、描述、更新时间等)

## 环境设置与运行指南

### 环境要求

**通用环境:**
- JDK 8+
- Maven 3.6+
- MySQL 8.0+
- Redis 6.0+

**管理系统前端:**
- Node.js 16+
- npm 8+

**APP前端:**
- HBuilderX (推荐)
- 微信开发者工具 (小程序开发)

### 快速启动步骤

#### 1. 数据库准备
```bash
# 创建APP数据库
CREATE DATABASE zhisuo_app;
# 执行 zhisuo-app-backend/智索APP数据库设计.sql

# 创建管理系统数据库
CREATE DATABASE zhisuo_manager;
# 执行 zhisuo-manager-backend/src/main/resources/init.sql
```

#### 2. 启动APP后端服务
```bash
cd zhisuo-app-backend
# 修改 application.yml 中的数据库和Redis配置
mvn spring-boot:run
# 服务地址: http://localhost:8080
```

#### 3. 启动管理系统后端服务
```bash
cd zhisuo-manager-backend
# 修改 application.yml 中的数据库和Redis配置
mvn spring-boot:run
# 或者双击 start.bat (Windows)
# 服务地址: http://localhost:8081/manager
```

#### 4. 启动管理系统前端
```bash
cd zhisuo-manager-frontend
npm install
npm run dev
# 或者双击 start.bat (Windows)
# 访问地址: http://localhost:5173
# 默认账号: admin / admin123
```

#### 5. 启动APP前端
```bash
# 使用HBuilderX打开 zhisuo-app-frontend 项目
# 运行到浏览器、微信小程序或手机APP
```

### Redis配置

确保Redis服务正在运行，默认配置:
- 主机: localhost
- 端口: 6379
- 密码: gcf021206 (APP后端) / 无密码 (管理系统)

## 技术实现细节

[本部分将在后续开发每一个模块/功能时，自动填充该模块/功能的技术实现方案、关键代码片段说明、API端点设计等内容。]

### APP后端技术实现
- **认证机制**: 基于JWT的无状态认证，支持访问令牌和刷新令牌双令牌机制
- **数据访问**: 使用MyBatis-Plus简化CRUD操作，支持分页查询和条件构造器
- **缓存策略**: Redis缓存用户会话信息和热点数据，提升系统性能
- **异常处理**: 全局异常处理器统一处理业务异常和系统异常
- **接口设计**: RESTful API设计，统一返回格式Result<T>

### 管理系统技术实现
- **权限控制**: Spring Security + JWT实现细粒度权限控制
- **数据可视化**: ECharts图表展示用户统计和内容分析数据
- **响应式设计**: Element Plus组件库实现现代化管理界面
- **状态管理**: Pinia集中管理应用状态和用户信息

## 开发状态跟踪

| 模块/功能                    | 状态   | 负责人 | 计划完成日期 | 实际完成日期 | 备注与链接 |
|------------------------------|--------|--------|--------------|--------------|------------|
| APP用户认证模块              | 已完成 | AI     | -            | -            | 支持短信和密码登录 |
| APP内容浏览模块              | 已完成 | AI     | -            | -            | 首页、文章详情等页面 |
| APP热点发现模块              | 已完成 | AI     | -            | -            | 热点列表和详情页面 |
| APP个人中心模块              | 已完成 | AI     | -            | -            | 用户资料、密码修改 |
| APP收藏管理模块              | 已完成 | AI     | -            | -            | 收藏列表和管理 |
| APP分析功能模块              | 已完成 | AI     | -            | -            | 个人分析记录 |
| 管理系统认证授权模块         | 已完成 | AI     | -            | -            | JWT认证和权限控制 |
| 管理系统用户管理模块         | 已完成 | AI     | -            | -            | 用户CRUD和统计 |
| 管理系统内容管理模块         | 已完成 | AI     | -            | -            | 文章和话题管理 |
| 管理系统数据统计模块         | 已完成 | AI     | -            | -            | 图表展示和分析 |
| 管理系统系统管理模块         | 已完成 | AI     | -            | -            | 管理员和日志管理 |

## 代码检查与问题记录

[本部分用于记录代码检查结果和开发过程中遇到的问题及其解决方案。]

### 已知问题
1. **配置文件敏感信息**: 数据库密码和Redis密码直接写在配置文件中，建议使用环境变量或配置中心
2. **API文档**: 缺少完整的API文档，建议集成Swagger或类似工具
3. **单元测试**: 项目缺少单元测试覆盖，建议补充核心业务逻辑的测试用例
4. **日志规范**: 日志输出格式和级别需要统一规范

### 改进建议
1. **安全加固**: 实现接口限流、参数校验、SQL注入防护等安全措施
2. **性能优化**: 数据库查询优化、缓存策略优化、静态资源CDN等
3. **监控告警**: 集成应用监控和告警系统，及时发现和处理问题
4. **容器化部署**: 使用Docker容器化部署，提升部署效率和环境一致性

## 部署说明

### 开发环境部署
参考上述"环境设置与运行指南"章节

### 生产环境部署

#### 后端部署
```bash
# APP后端
cd zhisuo-app-backend
mvn clean package
java -jar target/zhisuo-app-backend-1.0.0.jar

# 管理系统后端
cd zhisuo-manager-backend
mvn clean package
java -jar target/zhisuo-manager-backend-1.0.0.jar
```

#### 前端部署
```bash
# 管理系统前端
cd zhisuo-manager-frontend
npm run build
# 将 dist 目录部署到Nginx等Web服务器

# APP前端
# 使用HBuilderX打包为小程序或APP安装包
```

#### Nginx配置示例
```nginx
# 管理系统前端
server {
    listen 80;
    server_name manager.zhisuo.com;
    root /var/www/zhisuo-manager-frontend/dist;
    index index.html;

    location /api/ {
        proxy_pass http://localhost:8081/;
    }
}
```

## 许可证

MIT License

## 联系方式

如有问题或建议，请联系开发团队。

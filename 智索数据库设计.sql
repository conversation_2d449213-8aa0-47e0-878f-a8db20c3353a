-- 智索应用数据库创建脚本
-- 适用于MySQL 8.0.31及以上版本
-- 创建日期: 2024-08-05
-- 包含智索APP和智索管理系统的完整数据库结构

-- =====================================================
-- 智索APP数据库 (zhisuo_app)
-- =====================================================

-- 创建智索APP数据库
DROP DATABASE IF EXISTS zhisuo_app;
CREATE DATABASE zhisuo_app DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE zhisuo_app;

-- =====================================================
-- 用户相关表
-- =====================================================

-- 用户基础信息表
CREATE TABLE `user` (
    `user_id` VARCHAR(32) NOT NULL COMMENT '用户ID',
    `phone` VARCHAR(20) NOT NULL COMMENT '手机号',
    `nickname` VARCHAR(50) DEFAULT NULL COMMENT '用户昵称',
    `avatar` VARCHAR(255) DEFAULT NULL COMMENT '头像URL',
    `password` VARCHAR(64) DEFAULT NULL COMMENT '密码(MD5)',
    `member_level` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '会员等级(0:普通,1:VIP)',
    `status` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '用户状态(0:禁用,1:正常)',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `last_login_time` DATETIME DEFAULT NULL COMMENT '最后登录时间',
    PRIMARY KEY (`user_id`),
    UNIQUE KEY `uk_phone` (`phone`),
    KEY `idx_create_time` (`create_time`),
    KEY `idx_status` (`status`),
    KEY `idx_member_level` (`member_level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户基础信息表';

-- 用户令牌表
CREATE TABLE `user_token` (
    `token_id` VARCHAR(32) NOT NULL COMMENT '令牌ID',
    `user_id` VARCHAR(32) NOT NULL COMMENT '用户ID',
    `access_token` TEXT NOT NULL COMMENT '访问令牌',
    `refresh_token` TEXT NOT NULL COMMENT '刷新令牌',
    `device_id` VARCHAR(64) NOT NULL COMMENT '设备ID',
    `device_type` VARCHAR(20) NOT NULL COMMENT '设备类型(android/ios/web)',
    `expires_at` DATETIME NOT NULL COMMENT '过期时间',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`token_id`),
    KEY `fk_user_token_user` (`user_id`),
    KEY `idx_device_id` (`device_id`),
    KEY `idx_expires_at` (`expires_at`),
    CONSTRAINT `fk_user_token_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户令牌表';

-- =====================================================
-- 内容相关表
-- =====================================================

-- 文章表
CREATE TABLE `article` (
    `article_id` VARCHAR(32) NOT NULL COMMENT '文章ID',
    `title` VARCHAR(255) NOT NULL COMMENT '文章标题',
    `description` TEXT DEFAULT NULL COMMENT '文章描述',
    `content` LONGTEXT DEFAULT NULL COMMENT '文章内容',
    `cover_image` VARCHAR(255) DEFAULT NULL COMMENT '封面图URL',
    `source` VARCHAR(100) NOT NULL COMMENT '来源(如:cctv.com)',
    `source_url` VARCHAR(500) DEFAULT NULL COMMENT '来源URL',
    `icon_url` VARCHAR(255) DEFAULT NULL COMMENT '图标URL',
    `view_count` INT NOT NULL DEFAULT 0 COMMENT '浏览量',
    `comment_count` INT NOT NULL DEFAULT 0 COMMENT '评论数',
    `like_count` INT NOT NULL DEFAULT 0 COMMENT '点赞数',
    `status` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '状态(0:下架,1:正常)',
    `publish_time` DATETIME NOT NULL COMMENT '发布时间',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`article_id`),
    KEY `idx_source` (`source`),
    KEY `idx_publish_time` (`publish_time`),
    KEY `idx_status` (`status`),
    KEY `idx_view_count` (`view_count`),
    KEY `idx_like_count` (`like_count`),
    FULLTEXT KEY `ft_title_content` (`title`, `content`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文章表';

-- 热点话题表
CREATE TABLE `hot_topic` (
    `topic_id` VARCHAR(32) NOT NULL COMMENT '话题ID',
    `title` VARCHAR(255) NOT NULL COMMENT '话题标题',
    `description` TEXT DEFAULT NULL COMMENT '话题描述',
    `source` VARCHAR(100) NOT NULL COMMENT '来源平台',
    `source_url` VARCHAR(500) DEFAULT NULL COMMENT '来源URL',
    `hot_value` VARCHAR(50) DEFAULT NULL COMMENT '热度值',
    `view_count` INT NOT NULL DEFAULT 0 COMMENT '阅读量',
    `search_count` INT NOT NULL DEFAULT 0 COMMENT '搜索量',
    `trend` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '趋势(1:上升,0:持平,-1:下降)',
    `rank` INT DEFAULT NULL COMMENT '热榜排名',
    `status` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '状态(0:下架,1:正常)',
    `collect_time` DATETIME NOT NULL COMMENT '收集时间',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`topic_id`),
    KEY `idx_source` (`source`),
    KEY `idx_collect_time` (`collect_time`),
    KEY `idx_status` (`status`),
    KEY `idx_rank` (`rank`),
    KEY `idx_hot_value` (`hot_value`),
    FULLTEXT KEY `ft_title_description` (`title`, `description`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='热点话题表';

-- 评论表
CREATE TABLE `comment` (
    `comment_id` VARCHAR(32) NOT NULL COMMENT '评论ID',
    `user_id` VARCHAR(32) NOT NULL COMMENT '用户ID',
    `content_id` VARCHAR(32) NOT NULL COMMENT '内容ID',
    `content_type` VARCHAR(20) NOT NULL COMMENT '内容类型(article/topic)',
    `content` TEXT NOT NULL COMMENT '评论内容',
    `parent_id` VARCHAR(32) DEFAULT NULL COMMENT '父评论ID',
    `like_count` INT NOT NULL DEFAULT 0 COMMENT '点赞数',
    `status` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '状态(0:删除,1:正常)',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`comment_id`),
    KEY `fk_comment_user` (`user_id`),
    KEY `idx_content` (`content_id`, `content_type`),
    KEY `idx_parent_id` (`parent_id`),
    KEY `idx_create_time` (`create_time`),
    CONSTRAINT `fk_comment_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='评论表';

-- =====================================================
-- 用户交互表
-- =====================================================

-- 用户收藏表
CREATE TABLE `user_favorite` (
    `favorite_id` VARCHAR(32) NOT NULL COMMENT '收藏ID',
    `user_id` VARCHAR(32) NOT NULL COMMENT '用户ID',
    `content_id` VARCHAR(32) NOT NULL COMMENT '内容ID',
    `content_type` VARCHAR(20) NOT NULL COMMENT '内容类型(article/topic)',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '收藏时间',
    PRIMARY KEY (`favorite_id`),
    KEY `fk_favorite_user` (`user_id`),
    KEY `idx_content` (`content_id`, `content_type`),
    UNIQUE KEY `uk_user_content` (`user_id`, `content_id`, `content_type`),
    CONSTRAINT `fk_favorite_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户收藏表';

-- 用户点赞表
CREATE TABLE `user_like` (
    `like_id` VARCHAR(32) NOT NULL COMMENT '点赞ID',
    `user_id` VARCHAR(32) NOT NULL COMMENT '用户ID',
    `content_id` VARCHAR(32) NOT NULL COMMENT '内容ID',
    `content_type` VARCHAR(20) NOT NULL COMMENT '内容类型(article/topic/comment)',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '点赞时间',
    PRIMARY KEY (`like_id`),
    KEY `fk_like_user` (`user_id`),
    KEY `idx_content` (`content_id`, `content_type`),
    UNIQUE KEY `uk_user_content` (`user_id`, `content_id`, `content_type`),
    CONSTRAINT `fk_like_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户点赞表';

-- =====================================================
-- 标签相关表
-- =====================================================

-- 标签表
CREATE TABLE `tag` (
    `tag_id` VARCHAR(32) NOT NULL COMMENT '标签ID',
    `name` VARCHAR(50) NOT NULL COMMENT '标签名称',
    `description` VARCHAR(255) DEFAULT NULL COMMENT '标签描述',
    `type` VARCHAR(20) NOT NULL COMMENT '标签类型',
    `color` VARCHAR(10) DEFAULT NULL COMMENT '标签颜色',
    `icon` VARCHAR(255) DEFAULT NULL COMMENT '标签图标',
    `usage_count` INT NOT NULL DEFAULT 0 COMMENT '使用次数',
    `status` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '状态(0:禁用,1:正常)',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`tag_id`),
    UNIQUE KEY `uk_name` (`name`),
    KEY `idx_type` (`type`),
    KEY `idx_usage_count` (`usage_count`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='标签表';

-- 内容标签关联表
CREATE TABLE `content_tag` (
    `relation_id` VARCHAR(32) NOT NULL COMMENT '关联ID',
    `content_id` VARCHAR(32) NOT NULL COMMENT '内容ID',
    `content_type` VARCHAR(20) NOT NULL COMMENT '内容类型(article/topic)',
    `tag_id` VARCHAR(32) NOT NULL COMMENT '标签ID',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`relation_id`),
    KEY `fk_content_tag_tag` (`tag_id`),
    KEY `idx_content` (`content_id`, `content_type`),
    UNIQUE KEY `uk_content_tag` (`content_id`, `content_type`, `tag_id`),
    CONSTRAINT `fk_content_tag_tag` FOREIGN KEY (`tag_id`) REFERENCES `tag` (`tag_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='内容标签关联表';

-- =====================================================
-- AI分析相关表
-- =====================================================

-- AI分析记录表
CREATE TABLE `ai_analysis` (
    `analysis_id` VARCHAR(32) NOT NULL COMMENT '分析ID',
    `user_id` VARCHAR(32) DEFAULT NULL COMMENT '用户ID(可为空表示匿名)',
    `content_id` VARCHAR(32) DEFAULT NULL COMMENT '内容ID',
    `content_type` VARCHAR(20) DEFAULT NULL COMMENT '内容类型(article/topic)',
    `analysis_type` VARCHAR(20) NOT NULL COMMENT '分析类型(summary/sentiment/keywords)',
    `input_text` TEXT NOT NULL COMMENT '输入文本',
    `result` JSON NOT NULL COMMENT '分析结果(JSON格式)',
    `model` VARCHAR(50) NOT NULL COMMENT '使用的AI模型',
    `tokens_used` INT NOT NULL DEFAULT 0 COMMENT '消耗的token数',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`analysis_id`),
    KEY `fk_analysis_user` (`user_id`),
    KEY `idx_content` (`content_id`, `content_type`),
    KEY `idx_analysis_type` (`analysis_type`),
    KEY `idx_create_time` (`create_time`),
    CONSTRAINT `fk_analysis_user` FOREIGN KEY (`user_id`) REFERENCES `user` (`user_id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI分析记录表';

-- =====================================================
-- 系统相关表
-- =====================================================

-- 系统通知表
CREATE TABLE `system_notice` (
    `notice_id` VARCHAR(32) NOT NULL COMMENT '通知ID',
    `title` VARCHAR(255) NOT NULL COMMENT '通知标题',
    `content` TEXT NOT NULL COMMENT '通知内容',
    `notice_type` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '通知类型(1:系统,2:活动,3:更新)',
    `image_url` VARCHAR(255) DEFAULT NULL COMMENT '图片URL',
    `target_url` VARCHAR(255) DEFAULT NULL COMMENT '跳转链接',
    `status` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '状态(0:下架,1:发布)',
    `publish_time` DATETIME NOT NULL COMMENT '发布时间',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`notice_id`),
    KEY `idx_notice_type` (`notice_type`),
    KEY `idx_status` (`status`),
    KEY `idx_publish_time` (`publish_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统通知表';

-- =====================================================
-- 智索管理系统数据库 (zhisuo_manager)
-- =====================================================

-- 创建智索管理系统数据库
DROP DATABASE IF EXISTS zhisuo_manager;
CREATE DATABASE zhisuo_manager DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE zhisuo_manager;

-- =====================================================
-- 管理员相关表
-- =====================================================

-- 管理员表
CREATE TABLE `admin` (
    `admin_id` VARCHAR(32) NOT NULL COMMENT '管理员ID',
    `username` VARCHAR(50) NOT NULL COMMENT '用户名',
    `password` VARCHAR(64) NOT NULL COMMENT '密码(MD5)',
    `real_name` VARCHAR(50) DEFAULT NULL COMMENT '真实姓名',
    `email` VARCHAR(100) DEFAULT NULL COMMENT '邮箱',
    `phone` VARCHAR(20) DEFAULT NULL COMMENT '手机号',
    `role` VARCHAR(20) NOT NULL DEFAULT 'admin' COMMENT '角色(admin/super_admin)',
    `status` TINYINT(1) NOT NULL DEFAULT 1 COMMENT '状态(0:禁用,1:正常)',
    `last_login_time` DATETIME DEFAULT NULL COMMENT '最后登录时间',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`admin_id`),
    UNIQUE KEY `uk_username` (`username`),
    KEY `idx_role` (`role`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='管理员表';

-- 操作日志表
CREATE TABLE `operation_log` (
    `log_id` VARCHAR(32) NOT NULL COMMENT '日志ID',
    `admin_id` VARCHAR(32) NOT NULL COMMENT '管理员ID',
    `operation` VARCHAR(100) NOT NULL COMMENT '操作类型',
    `module` VARCHAR(50) NOT NULL COMMENT '操作模块',
    `description` TEXT DEFAULT NULL COMMENT '操作描述',
    `ip_address` VARCHAR(45) DEFAULT NULL COMMENT 'IP地址',
    `user_agent` TEXT DEFAULT NULL COMMENT '用户代理',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
    PRIMARY KEY (`log_id`),
    KEY `fk_log_admin` (`admin_id`),
    KEY `idx_operation` (`operation`),
    KEY `idx_module` (`module`),
    KEY `idx_create_time` (`create_time`),
    CONSTRAINT `fk_log_admin` FOREIGN KEY (`admin_id`) REFERENCES `admin` (`admin_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='操作日志表';

-- =====================================================
-- 系统配置表
-- =====================================================

-- 系统配置表
CREATE TABLE `system_config` (
    `config_id` VARCHAR(32) NOT NULL COMMENT '配置ID',
    `config_key` VARCHAR(100) NOT NULL COMMENT '配置键',
    `config_value` TEXT DEFAULT NULL COMMENT '配置值',
    `description` VARCHAR(255) DEFAULT NULL COMMENT '配置描述',
    `type` VARCHAR(20) NOT NULL DEFAULT 'string' COMMENT '配置类型(string/number/boolean/json)',
    `category` VARCHAR(50) NOT NULL DEFAULT 'system' COMMENT '配置分类',
    `is_public` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否公开(0:私有,1:公开)',
    `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`config_id`),
    UNIQUE KEY `uk_config_key` (`config_key`),
    KEY `idx_category` (`category`),
    KEY `idx_is_public` (`is_public`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- =====================================================
-- 初始化数据
-- =====================================================

-- 插入默认管理员账号
INSERT INTO `admin` (`admin_id`, `username`, `password`, `real_name`, `role`, `status`) VALUES
('ADMIN001', 'admin', '21232f297a57a5a743894a0e4a801fc3', '系统管理员', 'super_admin', 1);

-- 插入默认系统配置
INSERT INTO `system_config` (`config_id`, `config_key`, `config_value`, `description`, `type`, `category`) VALUES
('CONFIG001', 'system.name', '智索应用管理系统', '系统名称', 'string', 'system'),
('CONFIG002', 'system.version', '1.0.0', '系统版本', 'string', 'system'),
('CONFIG003', 'ai.enabled', 'true', 'AI功能是否启用', 'boolean', 'ai'),
('CONFIG004', 'ai.model', 'Qwen/Qwen3-8B', '默认AI模型', 'string', 'ai'),
('CONFIG005', 'upload.max_size', '10485760', '文件上传最大大小(字节)', 'number', 'upload');

-- =====================================================
-- 创建索引和约束
-- =====================================================

-- 为智索APP数据库添加额外索引
USE zhisuo_app;

-- 为高频查询字段添加复合索引
ALTER TABLE `article` ADD INDEX `idx_status_publish_time` (`status`, `publish_time`);
ALTER TABLE `hot_topic` ADD INDEX `idx_status_rank` (`status`, `rank`);
ALTER TABLE `user_favorite` ADD INDEX `idx_user_create_time` (`user_id`, `create_time`);
ALTER TABLE `user_like` ADD INDEX `idx_user_create_time` (`user_id`, `create_time`);

-- 为智索管理系统数据库添加额外索引
USE zhisuo_manager;

-- 为操作日志添加复合索引
ALTER TABLE `operation_log` ADD INDEX `idx_admin_create_time` (`admin_id`, `create_time`);
ALTER TABLE `operation_log` ADD INDEX `idx_module_create_time` (`module`, `create_time`);

-- =====================================================
-- 创建视图
-- =====================================================

-- 智索APP数据库视图
USE zhisuo_app;

-- 用户统计视图
CREATE VIEW `v_user_stats` AS
SELECT
    DATE(create_time) as date,
    COUNT(*) as new_users,
    SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as active_users
FROM `user`
GROUP BY DATE(create_time);

-- 内容统计视图
CREATE VIEW `v_content_stats` AS
SELECT
    'article' as content_type,
    COUNT(*) as total_count,
    SUM(view_count) as total_views,
    SUM(like_count) as total_likes,
    SUM(comment_count) as total_comments
FROM `article` WHERE status = 1
UNION ALL
SELECT
    'hot_topic' as content_type,
    COUNT(*) as total_count,
    SUM(view_count) as total_views,
    0 as total_likes,
    0 as total_comments
FROM `hot_topic` WHERE status = 1;

-- 智索管理系统数据库视图
USE zhisuo_manager;

-- 管理员操作统计视图
CREATE VIEW `v_admin_operation_stats` AS
SELECT
    a.admin_id,
    a.username,
    a.real_name,
    COUNT(ol.log_id) as operation_count,
    MAX(ol.create_time) as last_operation_time
FROM `admin` a
LEFT JOIN `operation_log` ol ON a.admin_id = ol.admin_id
GROUP BY a.admin_id, a.username, a.real_name;

-- =====================================================
-- 存储过程
-- =====================================================

-- 智索APP数据库存储过程
USE zhisuo_app;

DELIMITER //

-- 清理过期令牌的存储过程
CREATE PROCEDURE `sp_clean_expired_tokens`()
BEGIN
    DELETE FROM `user_token` WHERE `expires_at` < NOW();
    SELECT ROW_COUNT() as deleted_count;
END //

-- 更新内容统计数据的存储过程
CREATE PROCEDURE `sp_update_content_stats`(
    IN content_id VARCHAR(32),
    IN content_type VARCHAR(20),
    IN stat_type VARCHAR(20)
)
BEGIN
    IF content_type = 'article' THEN
        CASE stat_type
            WHEN 'view' THEN
                UPDATE `article` SET `view_count` = `view_count` + 1 WHERE `article_id` = content_id;
            WHEN 'like' THEN
                UPDATE `article` SET `like_count` = `like_count` + 1 WHERE `article_id` = content_id;
            WHEN 'comment' THEN
                UPDATE `article` SET `comment_count` = `comment_count` + 1 WHERE `article_id` = content_id;
        END CASE;
    ELSEIF content_type = 'hot_topic' THEN
        CASE stat_type
            WHEN 'view' THEN
                UPDATE `hot_topic` SET `view_count` = `view_count` + 1 WHERE `topic_id` = content_id;
            WHEN 'search' THEN
                UPDATE `hot_topic` SET `search_count` = `search_count` + 1 WHERE `topic_id` = content_id;
        END CASE;
    END IF;
END //

DELIMITER ;

-- =====================================================
-- 触发器
-- =====================================================

-- 智索APP数据库触发器
USE zhisuo_app;

-- 用户创建时自动生成ID的触发器
DELIMITER //
CREATE TRIGGER `tr_user_before_insert`
BEFORE INSERT ON `user`
FOR EACH ROW
BEGIN
    IF NEW.user_id IS NULL OR NEW.user_id = '' THEN
        SET NEW.user_id = CONCAT('USR', LPAD(UNIX_TIMESTAMP(NOW()), 10, '0'), LPAD(CONNECTION_ID(), 6, '0'));
    END IF;
END //
DELIMITER ;

-- 标签使用次数更新触发器
DELIMITER //
CREATE TRIGGER `tr_content_tag_after_insert`
AFTER INSERT ON `content_tag`
FOR EACH ROW
BEGIN
    UPDATE `tag` SET `usage_count` = `usage_count` + 1 WHERE `tag_id` = NEW.tag_id;
END //

CREATE TRIGGER `tr_content_tag_after_delete`
AFTER DELETE ON `content_tag`
FOR EACH ROW
BEGIN
    UPDATE `tag` SET `usage_count` = `usage_count` - 1 WHERE `tag_id` = OLD.tag_id AND `usage_count` > 0;
END //
DELIMITER ;

-- 智索管理系统数据库触发器
USE zhisuo_manager;

-- 管理员创建时自动生成ID的触发器
DELIMITER //
CREATE TRIGGER `tr_admin_before_insert`
BEFORE INSERT ON `admin`
FOR EACH ROW
BEGIN
    IF NEW.admin_id IS NULL OR NEW.admin_id = '' THEN
        SET NEW.admin_id = CONCAT('ADM', LPAD(UNIX_TIMESTAMP(NOW()), 10, '0'), LPAD(CONNECTION_ID(), 6, '0'));
    END IF;
END //
DELIMITER ;

-- =====================================================
-- 权限设置
-- =====================================================

-- 创建应用用户
CREATE USER IF NOT EXISTS 'zhisuo_app'@'%' IDENTIFIED BY 'zhisuo_app_2024';
CREATE USER IF NOT EXISTS 'zhisuo_manager'@'%' IDENTIFIED BY 'zhisuo_manager_2024';

-- 授权智索APP数据库权限
GRANT SELECT, INSERT, UPDATE, DELETE ON zhisuo_app.* TO 'zhisuo_app'@'%';
GRANT EXECUTE ON zhisuo_app.* TO 'zhisuo_app'@'%';

-- 授权智索管理系统数据库权限
GRANT SELECT, INSERT, UPDATE, DELETE ON zhisuo_manager.* TO 'zhisuo_manager'@'%';
GRANT SELECT ON zhisuo_app.* TO 'zhisuo_manager'@'%';

-- 刷新权限
FLUSH PRIVILEGES;

-- =====================================================
-- 完成提示
-- =====================================================

SELECT '智索应用数据库创建完成！' as message;
SELECT '默认管理员账号: admin / admin123' as admin_info;

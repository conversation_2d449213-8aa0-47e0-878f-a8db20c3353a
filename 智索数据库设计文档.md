# 智索应用数据库设计文档

## 1. 数据库概述

本文档描述智索应用生态系统的完整数据库设计，包含智索APP和智索管理系统的所有数据表结构、关系设计以及字段定义。智索应用是一个集用户管理、内容展示、热点分析、AI智能分析和个性化推荐于一体的应用平台。

## 2. 数据库设计原则

- 遵循第三范式设计，减少数据冗余
- 保证数据完整性与一致性
- 考虑系统扩展性与性能需求
- 确保数据安全与用户隐私保护
- 支持高并发访问和大数据量存储
- 便于数据分析和统计查询

## 3. 数据库环境

### 3.1 技术规格
- **数据库类型**: MySQL 8.0.31+
- **字符集**: utf8mb4
- **排序规则**: utf8mb4_unicode_ci
- **存储引擎**: InnoDB
- **时区**: UTC

### 3.2 数据库实例
- **智索APP数据库**: `zhisuo_app`
- **智索管理系统数据库**: `zhisuo_manager`

## 4. 数据库表结构设计

### 4.1 智索APP数据库 (zhisuo_app)

#### 4.1.1 用户相关表

##### 用户基础信息表 (user)

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 主键 | 索引 | 说明 |
|--------|----------|------|----------|--------|------|------|------|
| user_id | VARCHAR | 32 | NOT NULL | | PK | | 用户ID |
| phone | VARCHAR | 20 | NOT NULL | | | UNI | 手机号 |
| nickname | VARCHAR | 50 | NULL | | | | 用户昵称 |
| avatar | VARCHAR | 255 | NULL | | | | 头像URL |
| password | VARCHAR | 64 | NULL | | | | 密码(MD5) |
| member_level | TINYINT | 1 | NOT NULL | 0 | | | 会员等级(0:普通,1:VIP) |
| status | TINYINT | 1 | NOT NULL | 1 | | | 用户状态(0:禁用,1:正常) |
| create_time | DATETIME | | NOT NULL | CURRENT_TIMESTAMP | | | 创建时间 |
| update_time | DATETIME | | NOT NULL | CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | | | 更新时间 |
| last_login_time | DATETIME | | NULL | | | | 最后登录时间 |

**索引设计**:
- PRIMARY KEY (`user_id`)
- UNIQUE KEY `uk_phone` (`phone`)
- KEY `idx_create_time` (`create_time`)
- KEY `idx_status` (`status`)
- KEY `idx_member_level` (`member_level`)

##### 用户令牌表 (user_token)

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 主键 | 索引 | 说明 |
|--------|----------|------|----------|--------|------|------|------|
| token_id | VARCHAR | 32 | NOT NULL | | PK | | 令牌ID |
| user_id | VARCHAR | 32 | NOT NULL | | | FK | 用户ID |
| access_token | TEXT | | NOT NULL | | | | 访问令牌 |
| refresh_token | TEXT | | NOT NULL | | | | 刷新令牌 |
| device_id | VARCHAR | 64 | NOT NULL | | | | 设备ID |
| device_type | VARCHAR | 20 | NOT NULL | | | | 设备类型(android/ios/web) |
| expires_at | DATETIME | | NOT NULL | | | | 过期时间 |
| create_time | DATETIME | | NOT NULL | CURRENT_TIMESTAMP | | | 创建时间 |
| update_time | DATETIME | | NOT NULL | CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | | | 更新时间 |

**索引设计**:
- PRIMARY KEY (`token_id`)
- KEY `fk_user_token_user` (`user_id`)
- KEY `idx_device_id` (`device_id`)
- KEY `idx_expires_at` (`expires_at`)

#### 4.1.2 内容相关表

##### 文章表 (article)

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 主键 | 索引 | 说明 |
|--------|----------|------|----------|--------|------|------|------|
| article_id | VARCHAR | 32 | NOT NULL | | PK | | 文章ID |
| title | VARCHAR | 255 | NOT NULL | | | | 文章标题 |
| description | TEXT | | NULL | | | | 文章描述 |
| content | LONGTEXT | | NULL | | | | 文章内容 |
| cover_image | VARCHAR | 255 | NULL | | | | 封面图URL |
| source | VARCHAR | 100 | NOT NULL | | | | 来源(如:cctv.com) |
| source_url | VARCHAR | 500 | NULL | | | | 来源URL |
| icon_url | VARCHAR | 255 | NULL | | | | 图标URL |
| view_count | INT | | NOT NULL | 0 | | | 浏览量 |
| comment_count | INT | | NOT NULL | 0 | | | 评论数 |
| like_count | INT | | NOT NULL | 0 | | | 点赞数 |
| status | TINYINT | 1 | NOT NULL | 1 | | | 状态(0:下架,1:正常) |
| publish_time | DATETIME | | NOT NULL | | | | 发布时间 |
| create_time | DATETIME | | NOT NULL | CURRENT_TIMESTAMP | | | 创建时间 |
| update_time | DATETIME | | NOT NULL | CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | | | 更新时间 |

**索引设计**:
- PRIMARY KEY (`article_id`)
- KEY `idx_source` (`source`)
- KEY `idx_publish_time` (`publish_time`)
- KEY `idx_status` (`status`)
- KEY `idx_view_count` (`view_count`)
- KEY `idx_like_count` (`like_count`)
- FULLTEXT KEY `ft_title_content` (`title`, `content`)

##### 热点话题表 (hot_topic)

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 主键 | 索引 | 说明 |
|--------|----------|------|----------|--------|------|------|------|
| topic_id | VARCHAR | 32 | NOT NULL | | PK | | 话题ID |
| title | VARCHAR | 255 | NOT NULL | | | | 话题标题 |
| description | TEXT | | NULL | | | | 话题描述 |
| source | VARCHAR | 100 | NOT NULL | | | | 来源平台 |
| source_url | VARCHAR | 500 | NULL | | | | 来源URL |
| hot_value | VARCHAR | 50 | NULL | | | | 热度值 |
| view_count | INT | | NOT NULL | 0 | | | 阅读量 |
| search_count | INT | | NOT NULL | 0 | | | 搜索量 |
| trend | TINYINT | 1 | NOT NULL | 0 | | | 趋势(1:上升,0:持平,-1:下降) |
| rank | INT | | NULL | | | | 热榜排名 |
| status | TINYINT | 1 | NOT NULL | 1 | | | 状态(0:下架,1:正常) |
| collect_time | DATETIME | | NOT NULL | | | | 收集时间 |
| create_time | DATETIME | | NOT NULL | CURRENT_TIMESTAMP | | | 创建时间 |
| update_time | DATETIME | | NOT NULL | CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | | | 更新时间 |

**索引设计**:
- PRIMARY KEY (`topic_id`)
- KEY `idx_source` (`source`)
- KEY `idx_collect_time` (`collect_time`)
- KEY `idx_status` (`status`)
- KEY `idx_rank` (`rank`)
- KEY `idx_hot_value` (`hot_value`)
- FULLTEXT KEY `ft_title_description` (`title`, `description`)

##### 评论表 (comment)

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 主键 | 索引 | 说明 |
|--------|----------|------|----------|--------|------|------|------|
| comment_id | VARCHAR | 32 | NOT NULL | | PK | | 评论ID |
| user_id | VARCHAR | 32 | NOT NULL | | | FK | 用户ID |
| content_id | VARCHAR | 32 | NOT NULL | | | | 内容ID |
| content_type | VARCHAR | 20 | NOT NULL | | | | 内容类型(article/topic) |
| content | TEXT | | NOT NULL | | | | 评论内容 |
| parent_id | VARCHAR | 32 | NULL | | | | 父评论ID |
| like_count | INT | | NOT NULL | 0 | | | 点赞数 |
| status | TINYINT | 1 | NOT NULL | 1 | | | 状态(0:删除,1:正常) |
| create_time | DATETIME | | NOT NULL | CURRENT_TIMESTAMP | | | 创建时间 |
| update_time | DATETIME | | NOT NULL | CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | | | 更新时间 |

**索引设计**:
- PRIMARY KEY (`comment_id`)
- KEY `fk_comment_user` (`user_id`)
- KEY `idx_content` (`content_id`, `content_type`)
- KEY `idx_parent_id` (`parent_id`)
- KEY `idx_create_time` (`create_time`)

#### 4.1.3 用户交互表

##### 用户收藏表 (user_favorite)

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 主键 | 索引 | 说明 |
|--------|----------|------|----------|--------|------|------|------|
| favorite_id | VARCHAR | 32 | NOT NULL | | PK | | 收藏ID |
| user_id | VARCHAR | 32 | NOT NULL | | | FK | 用户ID |
| content_id | VARCHAR | 32 | NOT NULL | | | | 内容ID |
| content_type | VARCHAR | 20 | NOT NULL | | | | 内容类型(article/topic) |
| create_time | DATETIME | | NOT NULL | CURRENT_TIMESTAMP | | | 收藏时间 |

**索引设计**:
- PRIMARY KEY (`favorite_id`)
- KEY `fk_favorite_user` (`user_id`)
- KEY `idx_content` (`content_id`, `content_type`)
- UNIQUE KEY `uk_user_content` (`user_id`, `content_id`, `content_type`)

##### 用户点赞表 (user_like)

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 主键 | 索引 | 说明 |
|--------|----------|------|----------|--------|------|------|------|
| like_id | VARCHAR | 32 | NOT NULL | | PK | | 点赞ID |
| user_id | VARCHAR | 32 | NOT NULL | | | FK | 用户ID |
| content_id | VARCHAR | 32 | NOT NULL | | | | 内容ID |
| content_type | VARCHAR | 20 | NOT NULL | | | | 内容类型(article/topic/comment) |
| create_time | DATETIME | | NOT NULL | CURRENT_TIMESTAMP | | | 点赞时间 |

**索引设计**:
- PRIMARY KEY (`like_id`)
- KEY `fk_like_user` (`user_id`)
- KEY `idx_content` (`content_id`, `content_type`)
- UNIQUE KEY `uk_user_content` (`user_id`, `content_id`, `content_type`)

#### 4.1.4 标签相关表

##### 标签表 (tag)

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 主键 | 索引 | 说明 |
|--------|----------|------|----------|--------|------|------|------|
| tag_id | VARCHAR | 32 | NOT NULL | | PK | | 标签ID |
| name | VARCHAR | 50 | NOT NULL | | | UNI | 标签名称 |
| description | VARCHAR | 255 | NULL | | | | 标签描述 |
| type | VARCHAR | 20 | NOT NULL | | | | 标签类型 |
| color | VARCHAR | 10 | NULL | | | | 标签颜色 |
| icon | VARCHAR | 255 | NULL | | | | 标签图标 |
| usage_count | INT | | NOT NULL | 0 | | | 使用次数 |
| status | TINYINT | 1 | NOT NULL | 1 | | | 状态(0:禁用,1:正常) |
| create_time | DATETIME | | NOT NULL | CURRENT_TIMESTAMP | | | 创建时间 |
| update_time | DATETIME | | NOT NULL | CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | | | 更新时间 |

**索引设计**:
- PRIMARY KEY (`tag_id`)
- UNIQUE KEY `uk_name` (`name`)
- KEY `idx_type` (`type`)
- KEY `idx_usage_count` (`usage_count`)
- KEY `idx_status` (`status`)

##### 内容标签关联表 (content_tag)

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 主键 | 索引 | 说明 |
|--------|----------|------|----------|--------|------|------|------|
| relation_id | VARCHAR | 32 | NOT NULL | | PK | | 关联ID |
| content_id | VARCHAR | 32 | NOT NULL | | | | 内容ID |
| content_type | VARCHAR | 20 | NOT NULL | | | | 内容类型(article/topic) |
| tag_id | VARCHAR | 32 | NOT NULL | | | FK | 标签ID |
| create_time | DATETIME | | NOT NULL | CURRENT_TIMESTAMP | | | 创建时间 |

**索引设计**:
- PRIMARY KEY (`relation_id`)
- KEY `fk_content_tag_tag` (`tag_id`)
- KEY `idx_content` (`content_id`, `content_type`)
- UNIQUE KEY `uk_content_tag` (`content_id`, `content_type`, `tag_id`)

#### 4.1.5 AI分析相关表

##### AI分析记录表 (ai_analysis)

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 主键 | 索引 | 说明 |
|--------|----------|------|----------|--------|------|------|------|
| analysis_id | VARCHAR | 32 | NOT NULL | | PK | | 分析ID |
| user_id | VARCHAR | 32 | NULL | | | FK | 用户ID(可为空表示匿名) |
| content_id | VARCHAR | 32 | NULL | | | | 内容ID |
| content_type | VARCHAR | 20 | NULL | | | | 内容类型(article/topic) |
| analysis_type | VARCHAR | 20 | NOT NULL | | | | 分析类型(summary/sentiment/keywords) |
| input_text | TEXT | | NOT NULL | | | | 输入文本 |
| result | JSON | | NOT NULL | | | | 分析结果(JSON格式) |
| model | VARCHAR | 50 | NOT NULL | | | | 使用的AI模型 |
| tokens_used | INT | | NOT NULL | 0 | | | 消耗的token数 |
| create_time | DATETIME | | NOT NULL | CURRENT_TIMESTAMP | | | 创建时间 |

**索引设计**:
- PRIMARY KEY (`analysis_id`)
- KEY `fk_analysis_user` (`user_id`)
- KEY `idx_content` (`content_id`, `content_type`)
- KEY `idx_analysis_type` (`analysis_type`)
- KEY `idx_create_time` (`create_time`)

#### 4.1.6 系统相关表

##### 系统通知表 (system_notice)

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 主键 | 索引 | 说明 |
|--------|----------|------|----------|--------|------|------|------|
| notice_id | VARCHAR | 32 | NOT NULL | | PK | | 通知ID |
| title | VARCHAR | 255 | NOT NULL | | | | 通知标题 |
| content | TEXT | | NOT NULL | | | | 通知内容 |
| notice_type | TINYINT | 1 | NOT NULL | 1 | | | 通知类型(1:系统,2:活动,3:更新) |
| image_url | VARCHAR | 255 | NULL | | | | 图片URL |
| target_url | VARCHAR | 255 | NULL | | | | 跳转链接 |
| status | TINYINT | 1 | NOT NULL | 1 | | | 状态(0:下架,1:发布) |
| publish_time | DATETIME | | NOT NULL | | | | 发布时间 |
| create_time | DATETIME | | NOT NULL | CURRENT_TIMESTAMP | | | 创建时间 |
| update_time | DATETIME | | NOT NULL | CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | | | 更新时间 |

**索引设计**:
- PRIMARY KEY (`notice_id`)
- KEY `idx_notice_type` (`notice_type`)
- KEY `idx_status` (`status`)
- KEY `idx_publish_time` (`publish_time`)

### 4.2 智索管理系统数据库 (zhisuo_manager)

#### 4.2.1 管理员相关表

##### 管理员表 (admin)

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 主键 | 索引 | 说明 |
|--------|----------|------|----------|--------|------|------|------|
| admin_id | VARCHAR | 32 | NOT NULL | | PK | | 管理员ID |
| username | VARCHAR | 50 | NOT NULL | | | UNI | 用户名 |
| password | VARCHAR | 64 | NOT NULL | | | | 密码(MD5) |
| real_name | VARCHAR | 50 | NULL | | | | 真实姓名 |
| email | VARCHAR | 100 | NULL | | | | 邮箱 |
| phone | VARCHAR | 20 | NULL | | | | 手机号 |
| role | VARCHAR | 20 | NOT NULL | 'admin' | | | 角色(admin/super_admin) |
| status | TINYINT | 1 | NOT NULL | 1 | | | 状态(0:禁用,1:正常) |
| last_login_time | DATETIME | | NULL | | | | 最后登录时间 |
| create_time | DATETIME | | NOT NULL | CURRENT_TIMESTAMP | | | 创建时间 |
| update_time | DATETIME | | NOT NULL | CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | | | 更新时间 |

**索引设计**:
- PRIMARY KEY (`admin_id`)
- UNIQUE KEY `uk_username` (`username`)
- KEY `idx_role` (`role`)
- KEY `idx_status` (`status`)

##### 操作日志表 (operation_log)

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 主键 | 索引 | 说明 |
|--------|----------|------|----------|--------|------|------|------|
| log_id | VARCHAR | 32 | NOT NULL | | PK | | 日志ID |
| admin_id | VARCHAR | 32 | NOT NULL | | | FK | 管理员ID |
| operation | VARCHAR | 100 | NOT NULL | | | | 操作类型 |
| module | VARCHAR | 50 | NOT NULL | | | | 操作模块 |
| description | TEXT | | NULL | | | | 操作描述 |
| ip_address | VARCHAR | 45 | NULL | | | | IP地址 |
| user_agent | TEXT | | NULL | | | | 用户代理 |
| create_time | DATETIME | | NOT NULL | CURRENT_TIMESTAMP | | | 操作时间 |

**索引设计**:
- PRIMARY KEY (`log_id`)
- KEY `fk_log_admin` (`admin_id`)
- KEY `idx_operation` (`operation`)
- KEY `idx_module` (`module`)
- KEY `idx_create_time` (`create_time`)

#### 4.2.2 系统配置表

##### 系统配置表 (system_config)

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 主键 | 索引 | 说明 |
|--------|----------|------|----------|--------|------|------|------|
| config_id | VARCHAR | 32 | NOT NULL | | PK | | 配置ID |
| config_key | VARCHAR | 100 | NOT NULL | | | UNI | 配置键 |
| config_value | TEXT | | NULL | | | | 配置值 |
| description | VARCHAR | 255 | NULL | | | | 配置描述 |
| type | VARCHAR | 20 | NOT NULL | 'string' | | | 配置类型(string/number/boolean/json) |
| category | VARCHAR | 50 | NOT NULL | 'system' | | | 配置分类 |
| is_public | TINYINT | 1 | NOT NULL | 0 | | | 是否公开(0:私有,1:公开) |
| create_time | DATETIME | | NOT NULL | CURRENT_TIMESTAMP | | | 创建时间 |
| update_time | DATETIME | | NOT NULL | CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | | | 更新时间 |

**索引设计**:
- PRIMARY KEY (`config_id`)
- UNIQUE KEY `uk_config_key` (`config_key`)
- KEY `idx_category` (`category`)
- KEY `idx_is_public` (`is_public`)

## 5. 数据库关系设计

### 5.1 外键约束

#### 智索APP数据库外键约束:
```sql
-- 用户令牌表外键
ALTER TABLE user_token ADD CONSTRAINT fk_user_token_user
FOREIGN KEY (user_id) REFERENCES user(user_id) ON DELETE CASCADE;

-- 评论表外键
ALTER TABLE comment ADD CONSTRAINT fk_comment_user
FOREIGN KEY (user_id) REFERENCES user(user_id) ON DELETE CASCADE;

-- 用户收藏表外键
ALTER TABLE user_favorite ADD CONSTRAINT fk_favorite_user
FOREIGN KEY (user_id) REFERENCES user(user_id) ON DELETE CASCADE;

-- 用户点赞表外键
ALTER TABLE user_like ADD CONSTRAINT fk_like_user
FOREIGN KEY (user_id) REFERENCES user(user_id) ON DELETE CASCADE;

-- 内容标签关联表外键
ALTER TABLE content_tag ADD CONSTRAINT fk_content_tag_tag
FOREIGN KEY (tag_id) REFERENCES tag(tag_id) ON DELETE CASCADE;

-- AI分析记录表外键
ALTER TABLE ai_analysis ADD CONSTRAINT fk_analysis_user
FOREIGN KEY (user_id) REFERENCES user(user_id) ON DELETE SET NULL;
```

#### 智索管理系统数据库外键约束:
```sql
-- 操作日志表外键
ALTER TABLE operation_log ADD CONSTRAINT fk_log_admin
FOREIGN KEY (admin_id) REFERENCES admin(admin_id) ON DELETE CASCADE;
```

### 5.2 数据库实体关系图

```
智索APP数据库 (zhisuo_app):

    ┌─────────────┐    1:N    ┌──────────────┐
    │    user     │◄─────────►│  user_token  │
    └─────────────┘           └──────────────┘
           │
           │ 1:N
           ▼
    ┌─────────────┐    N:M    ┌──────────────┐    N:M    ┌─────────────┐
    │user_favorite│◄─────────►│   article    │◄─────────►│ content_tag │
    └─────────────┘           └──────────────┘           └─────────────┘
           │                         │                          │
           │                         │ 1:N                      │ N:1
           │                         ▼                          ▼
    ┌─────────────┐           ┌──────────────┐           ┌─────────────┐
    │ user_like   │           │   comment    │           │     tag     │
    └─────────────┘           └──────────────┘           └─────────────┘
           │                                                     ▲
           │ N:M                                                 │ N:M
           ▼                                                     │
    ┌─────────────┐    N:M    ┌──────────────┐                  │
    │  hot_topic  │◄─────────►│ content_tag  │──────────────────┘
    └─────────────┘           └──────────────┘

智索管理系统数据库 (zhisuo_manager):

    ┌─────────────┐    1:N    ┌──────────────┐
    │    admin    │◄─────────►│operation_log │
    └─────────────┘           └──────────────┘
           │
           │ 1:N
           ▼
    ┌─────────────┐
    │system_config│
    └─────────────┘
```

## 6. 数据库性能优化

### 6.1 索引优化策略

1. **主键索引**: 所有表都使用VARCHAR(32)作为主键，便于分布式系统
2. **唯一索引**: 用户手机号、管理员用户名等唯一字段
3. **复合索引**: 用户收藏、点赞等多字段查询场景
4. **全文索引**: 文章标题和内容的搜索功能
5. **时间索引**: 创建时间、发布时间等时间相关查询

### 6.2 分区策略

对于大数据量表建议按时间分区:
```sql
-- 文章表按月分区
ALTER TABLE article PARTITION BY RANGE (YEAR(create_time)*100 + MONTH(create_time)) (
    PARTITION p202401 VALUES LESS THAN (202402),
    PARTITION p202402 VALUES LESS THAN (202403),
    -- ...
);

-- 热点话题表按月分区
ALTER TABLE hot_topic PARTITION BY RANGE (YEAR(collect_time)*100 + MONTH(collect_time)) (
    PARTITION p202401 VALUES LESS THAN (202402),
    PARTITION p202402 VALUES LESS THAN (202403),
    -- ...
);
```

### 6.3 缓存策略

1. **Redis缓存**: 用户会话、热点数据、统计数据
2. **应用缓存**: 标签列表、系统配置等相对静态数据
3. **CDN缓存**: 图片、静态资源等

## 7. 数据安全与备份

### 7.1 数据安全措施

1. **密码加密**: 用户密码和管理员密码使用MD5加密存储
2. **敏感数据**: 手机号等敏感信息需要脱敏处理
3. **访问控制**: 数据库用户权限最小化原则
4. **审计日志**: 管理员操作日志完整记录

### 7.2 备份策略

1. **全量备份**: 每日凌晨进行全量备份
2. **增量备份**: 每小时进行增量备份
3. **异地备份**: 重要数据异地存储
4. **恢复测试**: 定期进行数据恢复测试

## 8. 数据库维护

### 8.1 定期维护任务

1. **统计信息更新**: 定期更新表统计信息
2. **索引重建**: 定期重建碎片化严重的索引
3. **数据清理**: 清理过期的令牌、日志等数据
4. **性能监控**: 监控慢查询、锁等待等性能指标

### 8.2 监控指标

1. **连接数**: 数据库连接数监控
2. **查询性能**: 慢查询日志分析
3. **存储空间**: 数据库存储空间使用情况
4. **复制延迟**: 主从复制延迟监控

### 4.2 智索管理系统数据库 (zhisuo_manager)

#### 4.2.1 管理员相关表

##### 管理员表 (admin)

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 主键 | 索引 | 说明 |
|--------|----------|------|----------|--------|------|------|------|
| admin_id | VARCHAR | 32 | NOT NULL | | PK | | 管理员ID |
| username | VARCHAR | 50 | NOT NULL | | | UNI | 用户名 |
| password | VARCHAR | 64 | NOT NULL | | | | 密码(MD5) |
| real_name | VARCHAR | 50 | NULL | | | | 真实姓名 |
| email | VARCHAR | 100 | NULL | | | | 邮箱 |
| phone | VARCHAR | 20 | NULL | | | | 手机号 |
| role | VARCHAR | 20 | NOT NULL | 'admin' | | | 角色(admin/super_admin) |
| status | TINYINT | 1 | NOT NULL | 1 | | | 状态(0:禁用,1:正常) |
| last_login_time | DATETIME | | NULL | | | | 最后登录时间 |
| create_time | DATETIME | | NOT NULL | CURRENT_TIMESTAMP | | | 创建时间 |
| update_time | DATETIME | | NOT NULL | CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | | | 更新时间 |

**索引设计**:
- PRIMARY KEY (`admin_id`)
- UNIQUE KEY `uk_username` (`username`)
- KEY `idx_role` (`role`)
- KEY `idx_status` (`status`)

##### 操作日志表 (operation_log)

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 主键 | 索引 | 说明 |
|--------|----------|------|----------|--------|------|------|------|
| log_id | VARCHAR | 32 | NOT NULL | | PK | | 日志ID |
| admin_id | VARCHAR | 32 | NOT NULL | | | FK | 管理员ID |
| operation | VARCHAR | 100 | NOT NULL | | | | 操作类型 |
| module | VARCHAR | 50 | NOT NULL | | | | 操作模块 |
| description | TEXT | | NULL | | | | 操作描述 |
| ip_address | VARCHAR | 45 | NULL | | | | IP地址 |
| user_agent | TEXT | | NULL | | | | 用户代理 |
| create_time | DATETIME | | NOT NULL | CURRENT_TIMESTAMP | | | 操作时间 |

**索引设计**:
- PRIMARY KEY (`log_id`)
- KEY `fk_log_admin` (`admin_id`)
- KEY `idx_operation` (`operation`)
- KEY `idx_module` (`module`)
- KEY `idx_create_time` (`create_time`)

#### 4.2.2 系统配置表

##### 系统配置表 (system_config)

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 主键 | 索引 | 说明 |
|--------|----------|------|----------|--------|------|------|------|
| config_id | VARCHAR | 32 | NOT NULL | | PK | | 配置ID |
| config_key | VARCHAR | 100 | NOT NULL | | | UNI | 配置键 |
| config_value | TEXT | | NULL | | | | 配置值 |
| description | VARCHAR | 255 | NULL | | | | 配置描述 |
| type | VARCHAR | 20 | NOT NULL | 'string' | | | 配置类型(string/number/boolean/json) |
| category | VARCHAR | 50 | NOT NULL | 'system' | | | 配置分类 |
| is_public | TINYINT | 1 | NOT NULL | 0 | | | 是否公开(0:私有,1:公开) |
| create_time | DATETIME | | NOT NULL | CURRENT_TIMESTAMP | | | 创建时间 |
| update_time | DATETIME | | NOT NULL | CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | | | 更新时间 |

**索引设计**:
- PRIMARY KEY (`config_id`)
- UNIQUE KEY `uk_config_key` (`config_key`)
- KEY `idx_category` (`category`)
- KEY `idx_is_public` (`is_public`)

## 5. 数据库关系设计

### 5.1 外键约束

#### 智索APP数据库外键约束:
```sql
-- 用户令牌表外键
ALTER TABLE user_token ADD CONSTRAINT fk_user_token_user
FOREIGN KEY (user_id) REFERENCES user(user_id) ON DELETE CASCADE;

-- 评论表外键
ALTER TABLE comment ADD CONSTRAINT fk_comment_user
FOREIGN KEY (user_id) REFERENCES user(user_id) ON DELETE CASCADE;

-- 用户收藏表外键
ALTER TABLE user_favorite ADD CONSTRAINT fk_favorite_user
FOREIGN KEY (user_id) REFERENCES user(user_id) ON DELETE CASCADE;

-- 用户点赞表外键
ALTER TABLE user_like ADD CONSTRAINT fk_like_user
FOREIGN KEY (user_id) REFERENCES user(user_id) ON DELETE CASCADE;

-- 内容标签关联表外键
ALTER TABLE content_tag ADD CONSTRAINT fk_content_tag_tag
FOREIGN KEY (tag_id) REFERENCES tag(tag_id) ON DELETE CASCADE;

-- AI分析记录表外键
ALTER TABLE ai_analysis ADD CONSTRAINT fk_analysis_user
FOREIGN KEY (user_id) REFERENCES user(user_id) ON DELETE SET NULL;
```

#### 智索管理系统数据库外键约束:
```sql
-- 操作日志表外键
ALTER TABLE operation_log ADD CONSTRAINT fk_log_admin
FOREIGN KEY (admin_id) REFERENCES admin(admin_id) ON DELETE CASCADE;
```

### 5.2 数据库实体关系图

```
智索APP数据库 (zhisuo_app):

    ┌─────────────┐    1:N    ┌──────────────┐
    │    user     │◄─────────►│  user_token  │
    └─────────────┘           └──────────────┘
           │
           │ 1:N
           ▼
    ┌─────────────┐    N:M    ┌──────────────┐    N:M    ┌─────────────┐
    │user_favorite│◄─────────►│   article    │◄─────────►│ content_tag │
    └─────────────┘           └──────────────┘           └─────────────┘
           │                         │                          │
           │                         │ 1:N                      │ N:1
           │                         ▼                          ▼
    ┌─────────────┐           ┌──────────────┐           ┌─────────────┐
    │ user_like   │           │   comment    │           │     tag     │
    └─────────────┘           └──────────────┘           └─────────────┘
           │                                                     ▲
           │ N:M                                                 │ N:M
           ▼                                                     │
    ┌─────────────┐    N:M    ┌──────────────┐                  │
    │  hot_topic  │◄─────────►│ content_tag  │──────────────────┘
    └─────────────┘           └──────────────┘

智索管理系统数据库 (zhisuo_manager):

    ┌─────────────┐    1:N    ┌──────────────┐
    │    admin    │◄─────────►│operation_log │
    └─────────────┘           └──────────────┘
           │
           │ 1:N
           ▼
    ┌─────────────┐
    │system_config│
    └─────────────┘
```

## 6. 数据库性能优化

### 6.1 索引优化策略

1. **主键索引**: 所有表都使用VARCHAR(32)作为主键，便于分布式系统
2. **唯一索引**: 用户手机号、管理员用户名等唯一字段
3. **复合索引**: 用户收藏、点赞等多字段查询场景
4. **全文索引**: 文章标题和内容的搜索功能
5. **时间索引**: 创建时间、发布时间等时间相关查询

### 6.2 分区策略

对于大数据量表建议按时间分区:
```sql
-- 文章表按月分区
ALTER TABLE article PARTITION BY RANGE (YEAR(create_time)*100 + MONTH(create_time)) (
    PARTITION p202401 VALUES LESS THAN (202402),
    PARTITION p202402 VALUES LESS THAN (202403),
    -- ...
);

-- 热点话题表按月分区
ALTER TABLE hot_topic PARTITION BY RANGE (YEAR(collect_time)*100 + MONTH(collect_time)) (
    PARTITION p202401 VALUES LESS THAN (202402),
    PARTITION p202402 VALUES LESS THAN (202403),
    -- ...
);
```

### 6.3 缓存策略

1. **Redis缓存**: 用户会话、热点数据、统计数据
2. **应用缓存**: 标签列表、系统配置等相对静态数据
3. **CDN缓存**: 图片、静态资源等

## 7. 数据安全与备份

### 7.1 数据安全措施

1. **密码加密**: 用户密码和管理员密码使用MD5加密存储
2. **敏感数据**: 手机号等敏感信息需要脱敏处理
3. **访问控制**: 数据库用户权限最小化原则
4. **审计日志**: 管理员操作日志完整记录

### 7.2 备份策略

1. **全量备份**: 每日凌晨进行全量备份
2. **增量备份**: 每小时进行增量备份
3. **异地备份**: 重要数据异地存储
4. **恢复测试**: 定期进行数据恢复测试

## 8. 数据库维护

### 8.1 定期维护任务

1. **统计信息更新**: 定期更新表统计信息
2. **索引重建**: 定期重建碎片化严重的索引
3. **数据清理**: 清理过期的令牌、日志等数据
4. **性能监控**: 监控慢查询、锁等待等性能指标

### 8.2 监控指标

1. **连接数**: 数据库连接数监控
2. **查询性能**: 慢查询日志分析
3. **存储空间**: 数据库存储空间使用情况
4. **复制延迟**: 主从复制延迟监控
